import express from 'express';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';
import HotelSearchService from '../services/hotelSearch.js';
import FlightSearchService from '../services/flightSearch.js';

/**
 * MCP (Model Context Protocol) Server for handling travel API calls
 * This server acts as a bridge between the LLM and external travel APIs
 */
class MCPServer {
  constructor() {
    this.app = express();
    this.hotelService = new HotelSearchService();
    this.flightService = new FlightSearchService();
    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * Setup Express middleware
   */
  setupMiddleware() {
    this.app.use(express.json());
    this.app.use((req, res, next) => {
      logger.info('MCP Request', { 
        method: req.method, 
        path: req.path, 
        body: req.body 
      });
      next();
    });
  }

  /**
   * Setup API routes
   */
  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    // Hotel search endpoint
    this.app.post('/search/hotels', async (req, res) => {
      try {
        const { parameters } = req.body;
        const results = await this.hotelService.search(parameters);
        res.json({
          success: true,
          data: results,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error('Hotel search error', { error: error.message, parameters: req.body.parameters });
        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Flight search endpoint
    this.app.post('/search/flights', async (req, res) => {
      try {
        const { parameters } = req.body;
        const results = await this.flightService.search(parameters);
        res.json({
          success: true,
          data: results,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error('Flight search error', { error: error.message, parameters: req.body.parameters });
        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Generic tool execution endpoint for MCP
    this.app.post('/execute-tool', async (req, res) => {
      try {
        const { tool, parameters } = req.body;
        let result;

        switch (tool) {
          case 'search_hotels':
            result = await this.hotelService.search(parameters);
            break;
          case 'search_flights':
            result = await this.flightService.search(parameters);
            break;
          case 'get_hotel_details':
            result = await this.hotelService.getDetails(parameters.hotelId);
            break;
          case 'get_flight_details':
            result = await this.flightService.getDetails(parameters.flightId);
            break;
          default:
            throw new Error(`Unknown tool: ${tool}`);
        }

        res.json({
          success: true,
          tool,
          data: result,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error('Tool execution error', { 
          tool: req.body.tool, 
          error: error.message, 
          parameters: req.body.parameters 
        });
        res.status(500).json({
          success: false,
          error: error.message,
          tool: req.body.tool,
          timestamp: new Date().toISOString()
        });
      }
    });

    // List available tools
    this.app.get('/tools', (req, res) => {
      res.json({
        tools: [
          {
            name: 'search_hotels',
            description: 'Search for hotels based on location, dates, and preferences',
            parameters: {
              destination: 'string (required)',
              checkInDate: 'string (required, YYYY-MM-DD)',
              checkOutDate: 'string (required, YYYY-MM-DD)',
              guests: 'number (required)',
              rooms: 'number (optional, default: 1)',
              priceRange: 'string (optional: budget|mid-range|luxury)',
              amenities: 'array (optional)'
            }
          },
          {
            name: 'search_flights',
            description: 'Search for flights based on origin, destination, and travel dates',
            parameters: {
              origin: 'string (required)',
              destination: 'string (required)',
              departureDate: 'string (required, YYYY-MM-DD)',
              returnDate: 'string (optional, YYYY-MM-DD)',
              passengers: 'number (required)',
              class: 'string (optional: economy|business|first)',
              tripType: 'string (optional: one-way|round-trip)'
            }
          },
          {
            name: 'get_hotel_details',
            description: 'Get detailed information about a specific hotel',
            parameters: {
              hotelId: 'string (required)'
            }
          },
          {
            name: 'get_flight_details',
            description: 'Get detailed information about a specific flight',
            parameters: {
              flightId: 'string (required)'
            }
          }
        ]
      });
    });
  }

  /**
   * Start the MCP server
   * @param {number} port - Port to listen on
   * @returns {Promise<void>}
   */
  async start(port = config.mcp.serverPort) {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(port, () => {
          logger.info(`MCP Server started on port ${port}`);
          resolve();
        });
      } catch (error) {
        logger.error('Failed to start MCP server', { error: error.message });
        reject(error);
      }
    });
  }

  /**
   * Stop the MCP server
   * @returns {Promise<void>}
   */
  async stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          logger.info('MCP Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

export default MCPServer;
