import { v4 as uuidv4 } from 'uuid';
import <PERSON><PERSON> from 'joi';
import ConversationManager from '../services/conversationManager.js';
import MCPClient from '../services/mcpClient.js';
import logger from '../utils/logger.js';

/**
 * Chat Controller
 * Handles chat API endpoints and request/response processing
 */
class ChatController {
  constructor() {
    this.conversationManager = new ConversationManager();
    this.mcpClient = new MCPClient();
  }

  /**
   * Handle chat message
   * POST /api/chat
   */
  async handleChatMessage(req, res) {
    try {
      // Validate request
      const { error, value } = this.validateChatRequest(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Invalid request',
          details: error.details.map(d => d.message),
          timestamp: new Date().toISOString()
        });
      }

      const { message, sessionId: providedSessionId } = value;
      const sessionId = providedSessionId || uuidv4();

      logger.info('Processing chat message', { sessionId, messageLength: message.length });

      // Process message through conversation manager
      const result = await this.conversationManager.processMessage(sessionId, message);

      // Check if we need to make API calls
      if (result.state.phase === 'api_call' && result.state.hasAllParameters) {
        const apiResult = await this.handleApiCall(result.state);

        // Process API result through LLM
        const processedResult = await this.processApiResult(sessionId, apiResult, result.state);

        return res.json({
          success: true,
          sessionId,
          response: processedResult.response,
          state: processedResult.state,
          apiData: apiResult,
          timestamp: new Date().toISOString()
        });
      }

      // Return regular conversation response
      res.json({
        success: true,
        sessionId,
        response: result.response,
        state: result.state,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Chat message handling failed', {
        error: error.message,
        stack: error.stack,
        body: req.body
      });

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to process chat message',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get conversation state
   * GET /api/chat/:sessionId/state
   */
  async getConversationState(req, res) {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: 'Session ID is required',
          timestamp: new Date().toISOString()
        });
      }

      const state = this.conversationManager.getSessionState(sessionId);

      if (!state) {
        return res.status(404).json({
          success: false,
          error: 'Session not found',
          timestamp: new Date().toISOString()
        });
      }

      res.json({
        success: true,
        sessionId,
        state,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Failed to get conversation state', {
        error: error.message,
        sessionId: req.params.sessionId
      });

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Reset conversation
   * POST /api/chat/:sessionId/reset
   */
  async resetConversation(req, res) {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: 'Session ID is required',
          timestamp: new Date().toISOString()
        });
      }

      this.conversationManager.resetSession(sessionId);

      logger.info('Conversation reset', { sessionId });

      res.json({
        success: true,
        sessionId,
        message: 'Conversation reset successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Failed to reset conversation', {
        error: error.message,
        sessionId: req.params.sessionId
      });

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Health check endpoint
   * GET /api/health
   */
  async healthCheck(req, res) {
    try {
      // Test Bedrock connection
      const bedrockHealthy = await this.conversationManager.bedrockService.testConnection();

      // Test MCP connection
      const mcpHealthy = await this.mcpClient.testConnection();

      const health = {
        status: bedrockHealthy && mcpHealthy ? 'healthy' : 'degraded',
        services: {
          bedrock: bedrockHealthy ? 'healthy' : 'unhealthy',
          mcp: mcpHealthy ? 'healthy' : 'unhealthy'
        },
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      };

      const statusCode = health.status === 'healthy' ? 200 : 503;
      res.status(statusCode).json(health);

    } catch (error) {
      logger.error('Health check failed', { error: error.message });

      res.status(503).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Validate chat request
   * @param {Object} body - Request body
   * @returns {Object} - Validation result
   */
  validateChatRequest(body) {
    const schema = Joi.object({
      message: Joi.string().required().min(1).max(2000),
      sessionId: Joi.string().uuid().optional()
    });

    return schema.validate(body);
  }

  /**
   * Handle API call based on conversation state
   * @param {Object} state - Conversation state
   * @returns {Promise<Object>} - API result
   */
  async handleApiCall(state) {
    try {
      if (state.serviceType === 'hotel') {
        return await this.mcpClient.searchHotels(state.collectedParameters);
      } else if (state.serviceType === 'flight') {
        return await this.mcpClient.searchFlights(state.collectedParameters);
      } else if (state.serviceType === 'both') {
        // Handle combined search
        const [hotelResults, flightResults] = await Promise.all([
          this.mcpClient.searchHotels(this.extractHotelParameters(state.collectedParameters)),
          this.mcpClient.searchFlights(this.extractFlightParameters(state.collectedParameters))
        ]);

        return {
          hotels: hotelResults,
          flights: flightResults,
          combinedSearch: true,
          suggestions: this.generateCombinedSuggestions(hotelResults, flightResults, state.collectedParameters)
        };
      } else {
        throw new Error(`Unknown service type: ${state.serviceType}`);
      }
    } catch (error) {
      logger.error('API call failed', {
        error: error.message,
        serviceType: state.serviceType,
        parameters: state.collectedParameters
      });
      throw error;
    }
  }

  /**
   * Process API result through LLM
   * @param {string} sessionId - Session ID
   * @param {Object} apiResult - API result data
   * @param {Object} state - Conversation state
   * @returns {Promise<Object>} - Processed result
   */
  async processApiResult(sessionId, apiResult, state) {
    try {
      let systemPrompt;
      let userMessage;

      if (apiResult.combinedSearch) {
        systemPrompt = `You are a helpful travel assistant. The user has searched for both flights and hotels for their trip.
        Present the results in a friendly, organized way. Show the best flight and hotel combinations.

        Key points to highlight:
        - Best flight options with times and prices
        - Recommended hotels near their destination
        - Suggest combinations that work well together (e.g., arrival time vs check-in time)
        - Mention any special suggestions or deals
        - Ask if they want to refine either search or need more details

        Flight Results: ${JSON.stringify(apiResult.flights, null, 2)}
        Hotel Results: ${JSON.stringify(apiResult.hotels, null, 2)}
        Smart Suggestions: ${JSON.stringify(apiResult.suggestions, null, 2)}`;

        userMessage = `Here are your complete travel search results with both flights and hotels.`;
      } else {
        systemPrompt = `You are a helpful travel assistant. The user has searched for ${state.serviceType} and here are the results.
        Present the results in a friendly, organized way. Highlight the best options and ask if they need more information or want to refine their search.

        ${state.serviceType === 'hotel' ?
            'Also mention that you can help them find flights to get there if needed.' :
            'Also mention that you can help them find accommodation at their destination if needed.'}

        Search Results: ${JSON.stringify(apiResult, null, 2)}`;

        userMessage = `Here are the ${state.serviceType} search results based on your requirements.`;
      }

      const response = await this.conversationManager.bedrockService.invokeModel(
        userMessage,
        state.conversationHistory,
        systemPrompt
      );

      // Update conversation state
      const conversationState = this.conversationManager.sessions.get(sessionId);
      if (conversationState) {
        conversationState.update({
          phase: 'result_processing',
          lastApiResponse: apiResult
        });
        conversationState.addMessage('assistant', response);
        this.conversationManager.sessions.set(sessionId, conversationState);
      }

      return {
        response,
        state: conversationState ? conversationState.getSummary() : state
      };

    } catch (error) {
      logger.error('Failed to process API result', {
        error: error.message,
        sessionId
      });
      throw error;
    }
  }

  /**
   * Extract hotel parameters from combined parameters
   * @param {Object} parameters - Combined parameters
   * @returns {Object} - Hotel-specific parameters
   */
  extractHotelParameters(parameters) {
    return {
      destination: parameters.destination,
      checkInDate: parameters.checkInDate,
      checkOutDate: parameters.checkOutDate,
      guests: parameters.guests,
      rooms: parameters.rooms || 1,
      priceRange: parameters.priceRange,
      amenities: parameters.amenities
    };
  }

  /**
   * Extract flight parameters from combined parameters
   * @param {Object} parameters - Combined parameters
   * @returns {Object} - Flight-specific parameters
   */
  extractFlightParameters(parameters) {
    return {
      origin: parameters.origin,
      destination: parameters.destination,
      departureDate: parameters.departureDate,
      returnDate: parameters.returnDate,
      passengers: parameters.passengers,
      class: parameters.class || 'economy',
      tripType: parameters.tripType || (parameters.returnDate ? 'round-trip' : 'one-way')
    };
  }

  /**
   * Generate smart suggestions for combined travel
   * @param {Object} hotelResults - Hotel search results
   * @param {Object} flightResults - Flight search results
   * @param {Object} _parameters - Search parameters (unused but kept for future enhancements)
   * @returns {Object} - Smart suggestions
   */
  generateCombinedSuggestions(hotelResults, flightResults, _parameters) {
    const suggestions = {
      bestCombinations: [],
      timingTips: [],
      budgetTips: [],
      locationTips: []
    };

    // Generate best flight + hotel combinations
    if (hotelResults.hotels && flightResults.outboundFlights) {
      const topFlights = flightResults.outboundFlights.slice(0, 2);
      const topHotels = hotelResults.hotels.slice(0, 2);

      topFlights.forEach(flight => {
        topHotels.forEach(hotel => {
          suggestions.bestCombinations.push({
            flight: {
              id: flight.id,
              airline: flight.airline,
              price: flight.price,
              departureTime: flight.departureTime,
              arrivalTime: flight.arrivalTime
            },
            hotel: {
              id: hotel.id,
              name: hotel.name,
              pricePerNight: hotel.pricePerNight,
              rating: hotel.rating
            },
            totalCost: flight.price + hotel.totalPrice,
            recommendation: this.generateCombinationRecommendation(flight, hotel)
          });
        });
      });
    }

    // Generate timing tips
    if (flightResults.outboundFlights && flightResults.outboundFlights.length > 0) {
      const earliestFlight = flightResults.outboundFlights[0];
      suggestions.timingTips.push(
        `Your earliest flight arrives at ${earliestFlight.arrivalTime}. Most hotels allow check-in from 3:00 PM.`
      );
    }

    // Generate budget tips
    if (hotelResults.hotels && flightResults.outboundFlights) {
      const cheapestFlight = flightResults.outboundFlights.reduce((min, flight) =>
        flight.price < min.price ? flight : min
      );
      const cheapestHotel = hotelResults.hotels.reduce((min, hotel) =>
        hotel.pricePerNight < min.pricePerNight ? hotel : min
      );

      suggestions.budgetTips.push(
        `Budget option: ${cheapestFlight.airline} flight (${cheapestFlight.price}) + ${cheapestHotel.name} (${cheapestHotel.pricePerNight}/night)`
      );
    }

    return suggestions;
  }

  /**
   * Generate recommendation for flight + hotel combination
   * @param {Object} flight - Flight option
   * @param {Object} hotel - Hotel option
   * @returns {string} - Recommendation text
   */
  generateCombinationRecommendation(flight, hotel) {
    const reasons = [];

    if (hotel.rating >= 4.5) {
      reasons.push('highly rated hotel');
    }
    if (flight.stops === 0) {
      reasons.push('direct flight');
    }
    if (flight.price < 300) {
      reasons.push('good flight price');
    }
    if (hotel.pricePerNight < 150) {
      reasons.push('affordable accommodation');
    }

    return reasons.length > 0 ?
      `Recommended for: ${reasons.join(', ')}` :
      'Good overall combination';
  }
}

export default ChatController;
