import { v4 as uuidv4 } from 'uuid';
import <PERSON><PERSON> from 'joi';
import ConversationManager from '../services/conversationManager.js';
import MCPClient from '../services/mcpClient.js';
import logger from '../utils/logger.js';

/**
 * Chat Controller
 * Handles chat API endpoints and request/response processing
 */
class ChatController {
  constructor() {
    this.conversationManager = new ConversationManager();
    this.mcpClient = new MCPClient();
  }

  /**
   * Handle chat message
   * POST /api/chat
   */
  async handleChatMessage(req, res) {
    try {
      // Validate request
      const { error, value } = this.validateChatRequest(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Invalid request',
          details: error.details.map(d => d.message),
          timestamp: new Date().toISOString()
        });
      }

      const { message, sessionId: providedSessionId } = value;
      const sessionId = providedSessionId || uuidv4();

      logger.info('Processing chat message', { sessionId, messageLength: message.length });

      // Process message through conversation manager
      const result = await this.conversationManager.processMessage(sessionId, message);

      // Check if we need to make API calls
      if (result.state.phase === 'api_call' && result.state.hasAllParameters) {
        const apiResult = await this.handleApiCall(result.state);
        
        // Process API result through LLM
        const processedResult = await this.processApiResult(sessionId, apiResult, result.state);
        
        return res.json({
          success: true,
          sessionId,
          response: processedResult.response,
          state: processedResult.state,
          apiData: apiResult,
          timestamp: new Date().toISOString()
        });
      }

      // Return regular conversation response
      res.json({
        success: true,
        sessionId,
        response: result.response,
        state: result.state,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Chat message handling failed', { 
        error: error.message, 
        stack: error.stack,
        body: req.body 
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to process chat message',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get conversation state
   * GET /api/chat/:sessionId/state
   */
  async getConversationState(req, res) {
    try {
      const { sessionId } = req.params;
      
      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: 'Session ID is required',
          timestamp: new Date().toISOString()
        });
      }

      const state = this.conversationManager.getSessionState(sessionId);
      
      if (!state) {
        return res.status(404).json({
          success: false,
          error: 'Session not found',
          timestamp: new Date().toISOString()
        });
      }

      res.json({
        success: true,
        sessionId,
        state,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Failed to get conversation state', { 
        error: error.message, 
        sessionId: req.params.sessionId 
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Reset conversation
   * POST /api/chat/:sessionId/reset
   */
  async resetConversation(req, res) {
    try {
      const { sessionId } = req.params;
      
      if (!sessionId) {
        return res.status(400).json({
          success: false,
          error: 'Session ID is required',
          timestamp: new Date().toISOString()
        });
      }

      this.conversationManager.resetSession(sessionId);
      
      logger.info('Conversation reset', { sessionId });

      res.json({
        success: true,
        sessionId,
        message: 'Conversation reset successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Failed to reset conversation', { 
        error: error.message, 
        sessionId: req.params.sessionId 
      });
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Health check endpoint
   * GET /api/health
   */
  async healthCheck(req, res) {
    try {
      // Test Bedrock connection
      const bedrockHealthy = await this.conversationManager.bedrockService.testConnection();
      
      // Test MCP connection
      const mcpHealthy = await this.mcpClient.testConnection();

      const health = {
        status: bedrockHealthy && mcpHealthy ? 'healthy' : 'degraded',
        services: {
          bedrock: bedrockHealthy ? 'healthy' : 'unhealthy',
          mcp: mcpHealthy ? 'healthy' : 'unhealthy'
        },
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      };

      const statusCode = health.status === 'healthy' ? 200 : 503;
      res.status(statusCode).json(health);

    } catch (error) {
      logger.error('Health check failed', { error: error.message });
      
      res.status(503).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Validate chat request
   * @param {Object} body - Request body
   * @returns {Object} - Validation result
   */
  validateChatRequest(body) {
    const schema = Joi.object({
      message: Joi.string().required().min(1).max(2000),
      sessionId: Joi.string().uuid().optional()
    });

    return schema.validate(body);
  }

  /**
   * Handle API call based on conversation state
   * @param {Object} state - Conversation state
   * @returns {Promise<Object>} - API result
   */
  async handleApiCall(state) {
    try {
      if (state.serviceType === 'hotel') {
        return await this.mcpClient.searchHotels(state.collectedParameters);
      } else if (state.serviceType === 'flight') {
        return await this.mcpClient.searchFlights(state.collectedParameters);
      } else {
        throw new Error(`Unknown service type: ${state.serviceType}`);
      }
    } catch (error) {
      logger.error('API call failed', { 
        error: error.message, 
        serviceType: state.serviceType,
        parameters: state.collectedParameters 
      });
      throw error;
    }
  }

  /**
   * Process API result through LLM
   * @param {string} sessionId - Session ID
   * @param {Object} apiResult - API result data
   * @param {Object} state - Conversation state
   * @returns {Promise<Object>} - Processed result
   */
  async processApiResult(sessionId, apiResult, state) {
    try {
      const systemPrompt = `You are a helpful travel assistant. The user has searched for ${state.serviceType} and here are the results. 
      Present the results in a friendly, organized way. Highlight the best options and ask if they need more information or want to refine their search.
      
      Search Results: ${JSON.stringify(apiResult, null, 2)}`;

      const userMessage = `Here are the ${state.serviceType} search results based on your requirements.`;
      
      const response = await this.conversationManager.bedrockService.invokeModel(
        userMessage,
        state.conversationHistory,
        systemPrompt
      );

      // Update conversation state
      const conversationState = this.conversationManager.sessions.get(sessionId);
      if (conversationState) {
        conversationState.update({ 
          phase: 'result_processing',
          lastApiResponse: apiResult 
        });
        conversationState.addMessage('assistant', response);
        this.conversationManager.sessions.set(sessionId, conversationState);
      }

      return {
        response,
        state: conversationState ? conversationState.getSummary() : state
      };

    } catch (error) {
      logger.error('Failed to process API result', { 
        error: error.message, 
        sessionId 
      });
      throw error;
    }
  }
}

export default ChatController;
