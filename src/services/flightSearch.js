import axios from 'axios';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

/**
 * Flight Search Service
 * Handles flight search API calls and data processing
 */
class FlightSearchService {
  constructor() {
    this.apiKey = config.travelApis.flight.apiKey;
    this.baseUrl = config.travelApis.flight.baseUrl;
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 seconds timeout
    });
  }

  /**
   * Search for flights
   * @param {Object} parameters - Search parameters
   * @returns {Promise<Object>} - Search results
   */
  async search(parameters) {
    try {
      logger.info('Flight search initiated', { parameters });

      // Validate required parameters
      this.validateSearchParameters(parameters);

      // For demo purposes, we'll return mock data
      // In production, replace this with actual API call
      if (!this.apiKey || this.apiKey === 'your_flight_api_key_here') {
        return this.getMockFlightResults(parameters);
      }

      // Actual API call (uncomment and modify for real API)
      /*
      const response = await this.client.post('/search', {
        origin: parameters.origin,
        destination: parameters.destination,
        departure_date: parameters.departureDate,
        return_date: parameters.returnDate,
        passengers: parameters.passengers,
        class: parameters.class || 'economy',
        trip_type: parameters.tripType || 'one-way'
      });

      return this.processApiResponse(response.data);
      */

      return this.getMockFlightResults(parameters);
    } catch (error) {
      logger.error('Flight search failed', { 
        error: error.message, 
        parameters,
        stack: error.stack 
      });
      throw new Error(`Flight search failed: ${error.message}`);
    }
  }

  /**
   * Get flight details by ID
   * @param {string} flightId - Flight identifier
   * @returns {Promise<Object>} - Flight details
   */
  async getDetails(flightId) {
    try {
      logger.info('Getting flight details', { flightId });

      // For demo purposes, return mock data
      if (!this.apiKey || this.apiKey === 'your_flight_api_key_here') {
        return this.getMockFlightDetails(flightId);
      }

      // Actual API call (uncomment and modify for real API)
      /*
      const response = await this.client.get(`/flights/${flightId}`);
      return this.processFlightDetails(response.data);
      */

      return this.getMockFlightDetails(flightId);
    } catch (error) {
      logger.error('Failed to get flight details', { 
        error: error.message, 
        flightId,
        stack: error.stack 
      });
      throw new Error(`Failed to get flight details: ${error.message}`);
    }
  }

  /**
   * Validate search parameters
   * @param {Object} parameters - Search parameters
   * @throws {Error} - If validation fails
   */
  validateSearchParameters(parameters) {
    const required = ['origin', 'destination', 'departureDate', 'passengers'];
    const missing = required.filter(field => !parameters[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required parameters: ${missing.join(', ')}`);
    }

    // Validate dates
    const departureDate = new Date(parameters.departureDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (departureDate < today) {
      throw new Error('Departure date cannot be in the past');
    }

    if (parameters.returnDate) {
      const returnDate = new Date(parameters.returnDate);
      if (returnDate <= departureDate) {
        throw new Error('Return date must be after departure date');
      }
    }

    // Validate passengers
    if (parameters.passengers < 1 || parameters.passengers > 9) {
      throw new Error('Number of passengers must be between 1 and 9');
    }
  }

  /**
   * Generate mock flight search results for demo
   * @param {Object} parameters - Search parameters
   * @returns {Object} - Mock search results
   */
  getMockFlightResults(parameters) {
    const outboundFlights = [
      {
        id: 'flight_001',
        airline: 'SkyLine Airways',
        flightNumber: 'SL123',
        origin: parameters.origin,
        destination: parameters.destination,
        departureTime: '08:00',
        arrivalTime: '12:30',
        duration: '4h 30m',
        stops: 0,
        price: 299,
        currency: 'USD',
        class: parameters.class || 'economy',
        aircraft: 'Boeing 737',
        availableSeats: 45
      },
      {
        id: 'flight_002',
        airline: 'Global Express',
        flightNumber: 'GE456',
        origin: parameters.origin,
        destination: parameters.destination,
        departureTime: '14:15',
        arrivalTime: '19:45',
        duration: '5h 30m',
        stops: 1,
        price: 249,
        currency: 'USD',
        class: parameters.class || 'economy',
        aircraft: 'Airbus A320',
        availableSeats: 23
      },
      {
        id: 'flight_003',
        airline: 'Premium Air',
        flightNumber: 'PA789',
        origin: parameters.origin,
        destination: parameters.destination,
        departureTime: '18:30',
        arrivalTime: '23:00',
        duration: '4h 30m',
        stops: 0,
        price: 399,
        currency: 'USD',
        class: parameters.class || 'economy',
        aircraft: 'Boeing 787',
        availableSeats: 67
      }
    ];

    let returnFlights = [];
    if (parameters.returnDate) {
      returnFlights = [
        {
          id: 'flight_004',
          airline: 'SkyLine Airways',
          flightNumber: 'SL124',
          origin: parameters.destination,
          destination: parameters.origin,
          departureTime: '09:00',
          arrivalTime: '13:30',
          duration: '4h 30m',
          stops: 0,
          price: 299,
          currency: 'USD',
          class: parameters.class || 'economy',
          aircraft: 'Boeing 737',
          availableSeats: 52
        },
        {
          id: 'flight_005',
          airline: 'Global Express',
          flightNumber: 'GE457',
          origin: parameters.destination,
          destination: parameters.origin,
          departureTime: '15:20',
          arrivalTime: '20:50',
          duration: '5h 30m',
          stops: 1,
          price: 249,
          currency: 'USD',
          class: parameters.class || 'economy',
          aircraft: 'Airbus A320',
          availableSeats: 31
        }
      ];
    }

    return {
      outboundFlights,
      returnFlights,
      searchParameters: parameters,
      totalResults: outboundFlights.length,
      tripType: parameters.returnDate ? 'round-trip' : 'one-way',
      searchId: `search_${Date.now()}`,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Generate mock flight details for demo
   * @param {string} flightId - Flight ID
   * @returns {Object} - Mock flight details
   */
  getMockFlightDetails(flightId) {
    return {
      id: flightId,
      airline: 'SkyLine Airways',
      flightNumber: 'SL123',
      aircraft: 'Boeing 737-800',
      origin: {
        airport: 'John F. Kennedy International Airport',
        code: 'JFK',
        city: 'New York',
        terminal: 'Terminal 4'
      },
      destination: {
        airport: 'Los Angeles International Airport',
        code: 'LAX',
        city: 'Los Angeles',
        terminal: 'Terminal 2'
      },
      schedule: {
        departureTime: '08:00',
        arrivalTime: '12:30',
        duration: '4h 30m',
        timezone: 'Local time'
      },
      pricing: {
        economy: 299,
        business: 899,
        first: 1599,
        currency: 'USD'
      },
      amenities: [
        'In-flight WiFi',
        'Entertainment system',
        'Complimentary snacks',
        'Power outlets'
      ],
      baggage: {
        carryOn: 'Included',
        checked: '$30 for first bag',
        weight: '50 lbs maximum'
      },
      policies: {
        cancellation: 'Refundable within 24 hours',
        changes: 'Changes allowed with fee',
        checkin: 'Online check-in available 24 hours before departure'
      },
      seatMap: {
        totalSeats: 180,
        availableSeats: 45,
        seatConfiguration: '3-3'
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Calculate flight duration in minutes
   * @param {string} departureTime - Departure time (HH:MM)
   * @param {string} arrivalTime - Arrival time (HH:MM)
   * @returns {number} - Duration in minutes
   */
  calculateDuration(departureTime, arrivalTime) {
    const [depHour, depMin] = departureTime.split(':').map(Number);
    const [arrHour, arrMin] = arrivalTime.split(':').map(Number);
    
    const depMinutes = depHour * 60 + depMin;
    const arrMinutes = arrHour * 60 + arrMin;
    
    // Handle overnight flights
    return arrMinutes >= depMinutes ? 
      arrMinutes - depMinutes : 
      (24 * 60) - depMinutes + arrMinutes;
  }

  /**
   * Format duration from minutes to human readable
   * @param {number} minutes - Duration in minutes
   * @returns {string} - Formatted duration
   */
  formatDuration(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  }
}

export default FlightSearchService;
