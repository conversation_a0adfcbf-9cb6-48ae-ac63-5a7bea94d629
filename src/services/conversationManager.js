import { StateGraph, END } from '@langchain/langgraph';
import { ConversationState, ConversationPhase, ServiceType } from '../models/conversationState.js';
import BedrockService from './bedrock.js';
import logger from '../utils/logger.js';

class ConversationManager {
  constructor() {
    this.bedrockService = new BedrockService();
    this.sessions = new Map(); // In-memory session storage
    this.graph = this.createConversationGraph();
  }

  /**
   * Create the LangGraph state machine for conversation flow
   */
  createConversationGraph() {
    const workflow = new StateGraph({
      channels: {
        state: {
          value: (x, y) => y ?? x,
          default: () => new ConversationState(),
        },
        userMessage: {
          value: (x, y) => y ?? x,
          default: () => '',
        },
        response: {
          value: (x, y) => y ?? x,
          default: () => '',
        },
      },
    });

    // Define nodes
    workflow.addNode('greeting', this.handleGreeting.bind(this));
    workflow.addNode('serviceSelection', this.handleServiceSelection.bind(this));
    workflow.addNode('parameterCollection', this.handleParameterCollection.bind(this));
    workflow.addNode('apiCall', this.handleApiCall.bind(this));
    workflow.addNode('resultProcessing', this.handleResultProcessing.bind(this));
    workflow.addNode('followUp', this.handleFollowUp.bind(this));

    // Define edges
    workflow.addEdge('greeting', 'serviceSelection');
    workflow.addEdge('serviceSelection', 'parameterCollection');
    workflow.addEdge('parameterCollection', 'apiCall');
    workflow.addEdge('apiCall', 'resultProcessing');
    workflow.addEdge('resultProcessing', 'followUp');
    workflow.addEdge('followUp', END);

    // Set entry point
    workflow.setEntryPoint('greeting');

    return workflow.compile();
  }

  /**
   * Handle greeting phase
   */
  async handleGreeting(state) {
    const conversationState = state.state;
    const userMessage = state.userMessage;

    if (conversationState.phase === ConversationPhase.GREETING) {
      const systemPrompt = `You are a helpful travel assistant. Greet the user warmly and ask how you can help them with their travel needs. 
      You can help with hotel bookings and flight searches. Keep your response brief and friendly.`;

      const response = await this.bedrockService.invokeModel(
        userMessage,
        conversationState.conversationHistory,
        systemPrompt
      );

      conversationState.addMessage('user', userMessage);
      conversationState.addMessage('assistant', response);
      conversationState.update({ phase: ConversationPhase.SERVICE_SELECTION });

      return {
        state: conversationState,
        userMessage,
        response,
      };
    }

    return state;
  }

  /**
   * Handle service selection phase
   */
  async handleServiceSelection(state) {
    const conversationState = state.state;
    const userMessage = state.userMessage;

    if (conversationState.phase === ConversationPhase.SERVICE_SELECTION) {
      const systemPrompt = `You are a travel assistant. The user has expressed interest in travel services. 
      Analyze their message to determine if they want:
      1. Hotel booking/search
      2. Flight booking/search
      3. Both
      4. Still unclear
      
      Respond appropriately and guide them to specify their needs. If they mention specific travel needs, 
      acknowledge them and start collecting the necessary information.`;

      const response = await this.bedrockService.invokeModel(
        userMessage,
        conversationState.conversationHistory,
        systemPrompt
      );

      // Analyze user intent
      const intent = await this.analyzeUserIntent(userMessage, conversationState.conversationHistory);

      conversationState.addMessage('user', userMessage);
      conversationState.addMessage('assistant', response);
      conversationState.update({
        userIntent: intent,
        phase: ConversationPhase.PARAMETER_COLLECTION
      });

      // Set required parameters based on detected service
      if (intent.serviceType !== ServiceType.UNKNOWN) {
        conversationState.setRequiredParameters(intent.serviceType);
      }

      return {
        state: conversationState,
        userMessage,
        response,
      };
    }

    return state;
  }

  /**
   * Handle parameter collection phase
   */
  async handleParameterCollection(state) {
    const conversationState = state.state;
    const userMessage = state.userMessage;

    if (conversationState.phase === ConversationPhase.PARAMETER_COLLECTION) {
      // Extract parameters from user message
      const extractedParams = await this.extractParameters(userMessage, conversationState);

      // Update collected parameters
      Object.entries(extractedParams).forEach(([key, value]) => {
        if (value) {
          conversationState.setParameter(key, value);
        }
      });

      let response;
      if (conversationState.hasAllRequiredParameters()) {
        // All parameters collected, move to API call
        conversationState.update({ phase: ConversationPhase.API_CALL });
        response = `Great! I have all the information I need. Let me search for ${conversationState.serviceType} options for you.`;
      } else {
        // Still missing parameters
        const nextParam = conversationState.getNextMissingParameter();
        const systemPrompt = this.getParameterCollectionPrompt(nextParam, conversationState.serviceType);

        response = await this.bedrockService.invokeModel(
          userMessage,
          conversationState.conversationHistory,
          systemPrompt
        );
      }

      conversationState.addMessage('user', userMessage);
      conversationState.addMessage('assistant', response);

      return {
        state: conversationState,
        userMessage,
        response,
      };
    }

    return state;
  }

  /**
   * Process a user message through the conversation flow
   * @param {string} sessionId - Session identifier
   * @param {string} message - User message
   * @returns {Promise<Object>} - Response object
   */
  async processMessage(sessionId, message) {
    try {
      // Get or create session
      let conversationState = this.sessions.get(sessionId);
      if (!conversationState) {
        conversationState = new ConversationState();
        conversationState.sessionId = sessionId;
        this.sessions.set(sessionId, conversationState);
      }

      // Determine which handler to use based on current phase
      let response;
      switch (conversationState.phase) {
        case ConversationPhase.GREETING:
          response = await this.handleGreeting({
            state: conversationState,
            userMessage: message
          });
          break;
        case ConversationPhase.SERVICE_SELECTION:
          response = await this.handleServiceSelection({
            state: conversationState,
            userMessage: message
          });
          break;
        case ConversationPhase.PARAMETER_COLLECTION:
          response = await this.handleParameterCollection({
            state: conversationState,
            userMessage: message
          });
          break;
        default:
          response = await this.handleGeneral(conversationState, message);
      }

      // Update session
      this.sessions.set(sessionId, response.state);

      return {
        response: response.response,
        state: response.state.getSummary(),
      };
    } catch (error) {
      logger.error('Error processing message', {
        sessionId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Reset a conversation session
   * @param {string} sessionId - Session identifier
   */
  resetSession(sessionId) {
    const conversationState = this.sessions.get(sessionId);
    if (conversationState) {
      conversationState.reset();
      this.sessions.set(sessionId, conversationState);
    }
  }

  /**
   * Get session state
   * @param {string} sessionId - Session identifier
   * @returns {Object|null} - Session state summary
   */
  getSessionState(sessionId) {
    const conversationState = this.sessions.get(sessionId);
    return conversationState ? conversationState.getSummary() : null;
  }

  /**
   * Analyze user intent to determine service type
   * @param {string} message - User message
   * @param {Array} history - Conversation history
   * @returns {Promise<Object>} - Intent analysis result
   */
  async analyzeUserIntent(message, history) {
    const systemPrompt = `Analyze the user's message to determine their travel intent.
    Respond with a JSON object containing:
    {
      "serviceType": "hotel" | "flight" | "unknown",
      "confidence": 0.0-1.0,
      "extractedInfo": {}
    }

    Look for keywords like: hotel, accommodation, stay, room, flight, plane, airline, travel, book, search, etc.`;

    try {
      const response = await this.bedrockService.invokeModel(message, history, systemPrompt);
      const parsed = JSON.parse(response);
      return {
        serviceType: parsed.serviceType === 'hotel' ? ServiceType.HOTEL :
          parsed.serviceType === 'flight' ? ServiceType.FLIGHT : ServiceType.UNKNOWN,
        confidence: parsed.confidence || 0.5,
        extractedInfo: parsed.extractedInfo || {}
      };
    } catch (error) {
      logger.warn('Failed to parse intent analysis', { error: error.message });
      return {
        serviceType: ServiceType.UNKNOWN,
        confidence: 0.0,
        extractedInfo: {}
      };
    }
  }

  /**
   * Extract parameters from user message
   * @param {string} message - User message
   * @param {ConversationState} state - Current conversation state
   * @returns {Promise<Object>} - Extracted parameters
   */
  async extractParameters(message, state) {
    const serviceType = state.serviceType;
    let systemPrompt;

    if (serviceType === ServiceType.HOTEL) {
      systemPrompt = `Extract hotel search parameters from the user's message.
      Return a JSON object with these fields (only include if mentioned):
      {
        "destination": "city or location",
        "checkInDate": "YYYY-MM-DD format",
        "checkOutDate": "YYYY-MM-DD format",
        "guests": number,
        "rooms": number,
        "priceRange": "budget|mid-range|luxury",
        "amenities": ["wifi", "pool", "gym", etc.]
      }`;
    } else if (serviceType === ServiceType.FLIGHT) {
      systemPrompt = `Extract flight search parameters from the user's message.
      Return a JSON object with these fields (only include if mentioned):
      {
        "origin": "departure city/airport",
        "destination": "arrival city/airport",
        "departureDate": "YYYY-MM-DD format",
        "returnDate": "YYYY-MM-DD format (if round trip)",
        "passengers": number,
        "class": "economy|business|first",
        "tripType": "one-way|round-trip"
      }`;
    } else {
      return {};
    }

    try {
      const response = await this.bedrockService.invokeModel(message, state.conversationHistory, systemPrompt);
      return JSON.parse(response);
    } catch (error) {
      logger.warn('Failed to extract parameters', { error: error.message });
      return {};
    }
  }

  /**
   * Get parameter collection prompt
   * @param {string} parameter - Missing parameter name
   * @param {string} serviceType - Service type
   * @returns {string} - System prompt
   */
  getParameterCollectionPrompt(parameter, serviceType) {
    const prompts = {
      hotel: {
        destination: 'Ask the user where they would like to stay (city or specific location).',
        checkInDate: 'Ask the user for their check-in date.',
        checkOutDate: 'Ask the user for their check-out date.',
        guests: 'Ask the user how many guests will be staying.',
        rooms: 'Ask the user how many rooms they need.'
      },
      flight: {
        origin: 'Ask the user where they will be departing from (city or airport).',
        destination: 'Ask the user where they want to fly to (city or airport).',
        departureDate: 'Ask the user for their departure date.',
        passengers: 'Ask the user how many passengers will be traveling.',
        returnDate: 'Ask the user if they need a return flight and when.'
      }
    };

    const servicePrompts = prompts[serviceType] || {};
    const specificPrompt = servicePrompts[parameter] || `Ask the user for their ${parameter}.`;

    return `You are a helpful travel assistant. ${specificPrompt} Be friendly and conversational.`;
  }

  /**
   * Handle general conversation (fallback)
   * @param {ConversationState} state - Conversation state
   * @param {string} message - User message
   * @returns {Promise<Object>} - Response object
   */
  async handleGeneral(state, message) {
    const systemPrompt = `You are a helpful travel assistant. The user is asking about travel services.
    Respond helpfully and try to guide them towards hotel or flight search if appropriate.`;

    const response = await this.bedrockService.invokeModel(
      message,
      state.conversationHistory,
      systemPrompt
    );

    state.addMessage('user', message);
    state.addMessage('assistant', response);

    return {
      state,
      userMessage: message,
      response,
    };
  }
}

export default ConversationManager;
