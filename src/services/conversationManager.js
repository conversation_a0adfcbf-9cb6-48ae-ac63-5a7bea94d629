import { StateGraph, END } from '@langchain/langgraph';
import { ConversationState, ConversationPhase, ServiceType } from '../models/conversationState.js';
import BedrockService from './bedrock.js';
import logger from '../utils/logger.js';

class ConversationManager {
  constructor() {
    this.bedrockService = new BedrockService();
    this.sessions = new Map(); // In-memory session storage
    this.graph = this.createConversationGraph();
  }

  /**
   * Create the LangGraph state machine for conversation flow
   */
  createConversationGraph() {
    const workflow = new StateGraph({
      channels: {
        state: {
          value: (x, y) => y ?? x,
          default: () => new ConversationState(),
        },
        userMessage: {
          value: (x, y) => y ?? x,
          default: () => '',
        },
        response: {
          value: (x, y) => y ?? x,
          default: () => '',
        },
      },
    });

    // Define nodes
    workflow.addNode('greeting', this.handleGreeting.bind(this));
    workflow.addNode('serviceSelection', this.handleServiceSelection.bind(this));
    workflow.addNode('parameterCollection', this.handleParameterCollection.bind(this));
    workflow.addNode('apiCall', this.handleApiCall.bind(this));
    workflow.addNode('resultProcessing', this.handleResultProcessing.bind(this));
    workflow.addNode('followUp', this.handleFollowUp.bind(this));

    // Define edges
    workflow.addEdge('greeting', 'serviceSelection');
    workflow.addEdge('serviceSelection', 'parameterCollection');
    workflow.addEdge('parameterCollection', 'apiCall');
    workflow.addEdge('apiCall', 'resultProcessing');
    workflow.addEdge('resultProcessing', 'followUp');
    workflow.addEdge('followUp', END);

    // Set entry point
    workflow.setEntryPoint('greeting');

    return workflow.compile();
  }

  /**
   * Handle greeting phase - Now uses LangChain tools from the start
   */
  async handleGreeting(state) {
    const conversationState = state.state;
    const userMessage = state.userMessage;

    if (conversationState.phase === ConversationPhase.GREETING) {
      const systemPrompt = `You are a helpful travel assistant with access to powerful search tools.

      Available tools:
      - search_hotels: Search for hotel accommodations
      - search_flights: Search for flight options
      - search_combined_travel: Search for both flights and hotels for complete trip planning
      - get_hotel_details: Get detailed hotel information
      - get_flight_details: Get detailed flight information

      Greet the user warmly and help them with their travel needs. If they provide enough information
      in their first message, you can immediately use the appropriate search tools. Otherwise, ask
      clarifying questions to gather the information needed to help them.`;

      // Use LangChain with tools from the very beginning
      const result = await this.bedrockService.invokeModelWithTools(
        userMessage,
        conversationState.conversationHistory,
        systemPrompt
      );

      conversationState.addMessage('user', userMessage);
      conversationState.addMessage('assistant', result.response);

      // If tools were called in greeting, move to result processing
      if (result.hasToolCalls) {
        conversationState.update({
          phase: ConversationPhase.RESULT_PROCESSING,
          lastApiResponse: result.toolResults
        });
      } else {
        conversationState.update({ phase: ConversationPhase.SERVICE_SELECTION });
      }

      return {
        state: conversationState,
        userMessage,
        response: result.response,
        toolCalls: result.toolCalls,
        toolResults: result.toolResults
      };
    }

    return state;
  }

  /**
   * Handle service selection phase - Now uses LangChain tools throughout
   */
  async handleServiceSelection(state) {
    const conversationState = state.state;
    const userMessage = state.userMessage;

    if (conversationState.phase === ConversationPhase.SERVICE_SELECTION) {
      const systemPrompt = `You are a travel assistant with access to powerful search tools.

      Available tools:
      - search_hotels: Search for hotel accommodations
      - search_flights: Search for flight options
      - search_combined_travel: Search for both flights and hotels for complete trip planning
      - get_hotel_details: Get detailed hotel information
      - get_flight_details: Get detailed flight information

      The user has expressed interest in travel services. If they provide enough information,
      use the appropriate search tools immediately. If you need more information, ask clarifying
      questions. You can also suggest complementary services (e.g., if they want hotels, mention
      you can help with flights too).

      Always prioritize using tools when you have sufficient information rather than just collecting parameters.`;

      // Use LangChain with tools for service selection
      const result = await this.bedrockService.invokeModelWithTools(
        userMessage,
        conversationState.conversationHistory,
        systemPrompt
      );

      conversationState.addMessage('user', userMessage);
      conversationState.addMessage('assistant', result.response);

      // If tools were called, move to result processing
      if (result.hasToolCalls) {
        conversationState.update({
          phase: ConversationPhase.RESULT_PROCESSING,
          lastApiResponse: result.toolResults
        });
      } else {
        conversationState.update({ phase: ConversationPhase.PARAMETER_COLLECTION });
      }

      return {
        state: conversationState,
        userMessage,
        response: result.response,
        toolCalls: result.toolCalls,
        toolResults: result.toolResults
      };
    }

    return state;
  }

  /**
   * Handle parameter collection phase - Now uses LangChain tools throughout
   */
  async handleParameterCollection(state) {
    const conversationState = state.state;
    const userMessage = state.userMessage;

    if (conversationState.phase === ConversationPhase.PARAMETER_COLLECTION) {
      // Use LangChain with tools for all interactions - let Claude decide when to call tools
      const systemPrompt = this.getConversationalSystemPrompt(conversationState);

      // Use LangChain with tool calling for every interaction
      const result = await this.bedrockService.invokeModelWithTools(
        userMessage,
        conversationState.conversationHistory,
        systemPrompt
      );

      let response = result.response;

      // Check if tools were called
      if (result.hasToolCalls) {
        // Claude decided to call tools - update phase to result processing
        conversationState.update({
          phase: ConversationPhase.RESULT_PROCESSING,
          lastApiResponse: result.toolResults
        });

        // Include tool call information in response
        response = result.response;
      }

      conversationState.addMessage('user', userMessage);
      conversationState.addMessage('assistant', response);

      return {
        state: conversationState,
        userMessage,
        response,
        toolCalls: result.toolCalls,
        toolResults: result.toolResults
      };
    }

    return state;
  }

  /**
   * Process a user message through the conversation flow - Now uses LangChain tools throughout
   * @param {string} sessionId - Session identifier
   * @param {string} message - User message
   * @returns {Promise<Object>} - Response object
   */
  async processMessage(sessionId, message) {
    try {
      // Get or create session
      let conversationState = this.sessions.get(sessionId);
      if (!conversationState) {
        conversationState = new ConversationState();
        conversationState.sessionId = sessionId;
        this.sessions.set(sessionId, conversationState);
      }

      // Use LangChain tools for ALL conversation phases
      let result;
      switch (conversationState.phase) {
        case ConversationPhase.GREETING:
          result = await this.handleGreeting({
            state: conversationState,
            userMessage: message
          });
          break;
        case ConversationPhase.SERVICE_SELECTION:
          result = await this.handleServiceSelection({
            state: conversationState,
            userMessage: message
          });
          break;
        case ConversationPhase.PARAMETER_COLLECTION:
          result = await this.handleParameterCollection({
            state: conversationState,
            userMessage: message
          });
          break;
        default:
          result = await this.handleGeneral(conversationState, message);
      }

      // Update session
      this.sessions.set(sessionId, result.state);

      return {
        response: result.response,
        state: result.state.getSummary(),
        toolCalls: result.toolCalls,
        toolResults: result.toolResults
      };
    } catch (error) {
      logger.error('Error processing message', {
        sessionId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Reset a conversation session
   * @param {string} sessionId - Session identifier
   */
  resetSession(sessionId) {
    const conversationState = this.sessions.get(sessionId);
    if (conversationState) {
      conversationState.reset();
      this.sessions.set(sessionId, conversationState);
    }
  }

  /**
   * Get session state
   * @param {string} sessionId - Session identifier
   * @returns {Object|null} - Session state summary
   */
  getSessionState(sessionId) {
    const conversationState = this.sessions.get(sessionId);
    return conversationState ? conversationState.getSummary() : null;
  }

  /**
   * Analyze user intent to determine service type
   * @param {string} message - User message
   * @param {Array} history - Conversation history
   * @returns {Promise<Object>} - Intent analysis result
   */
  async analyzeUserIntent(message, history) {
    const systemPrompt = `Analyze the user's message to determine their travel intent.
    Respond with a JSON object containing:
    {
      "serviceType": "hotel" | "flight" | "both" | "unknown",
      "confidence": 0.0-1.0,
      "extractedInfo": {},
      "suggestCombined": boolean
    }

    Guidelines:
    - Use "hotel" if they only mention accommodation needs
    - Use "flight" if they only mention flight/travel needs
    - Use "both" if they mention needing both flights and hotels, or planning a complete trip
    - Use "unknown" if unclear
    - Set "suggestCombined" to true if they mention one service but could benefit from the other

    Look for keywords like:
    - Hotel: hotel, accommodation, stay, room, lodge, resort
    - Flight: flight, plane, airline, fly, travel, trip, journey
    - Combined: vacation, trip, travel package, business trip, holiday, getaway

    Examples:
    - "I need a hotel in Paris" → hotel (but suggestCombined: true)
    - "Book me a flight to Tokyo" → flight (but suggestCombined: true)
    - "I'm planning a trip to London" → both
    - "I need flights and hotels for my vacation" → both`;

    try {
      const response = await this.bedrockService.invokeModel(message, history, systemPrompt);
      const parsed = JSON.parse(response);
      return {
        serviceType: parsed.serviceType === 'hotel' ? ServiceType.HOTEL :
          parsed.serviceType === 'flight' ? ServiceType.FLIGHT :
            parsed.serviceType === 'both' ? ServiceType.BOTH : ServiceType.UNKNOWN,
        confidence: parsed.confidence || 0.5,
        extractedInfo: parsed.extractedInfo || {},
        suggestCombined: parsed.suggestCombined || false
      };
    } catch (error) {
      logger.warn('Failed to parse intent analysis', { error: error.message });
      return {
        serviceType: ServiceType.UNKNOWN,
        confidence: 0.0,
        extractedInfo: {},
        suggestCombined: false
      };
    }
  }

  /**
   * Extract parameters from user message
   * @param {string} message - User message
   * @param {ConversationState} state - Current conversation state
   * @returns {Promise<Object>} - Extracted parameters
   */
  async extractParameters(message, state) {
    const serviceType = state.serviceType;
    let systemPrompt;

    if (serviceType === ServiceType.HOTEL) {
      systemPrompt = `Extract hotel search parameters from the user's message.
      Return a JSON object with these fields (only include if mentioned):
      {
        "destination": "city or location",
        "checkInDate": "YYYY-MM-DD format",
        "checkOutDate": "YYYY-MM-DD format",
        "guests": number,
        "rooms": number,
        "priceRange": "budget|mid-range|luxury",
        "amenities": ["wifi", "pool", "gym", etc.]
      }`;
    } else if (serviceType === ServiceType.FLIGHT) {
      systemPrompt = `Extract flight search parameters from the user's message.
      Return a JSON object with these fields (only include if mentioned):
      {
        "origin": "departure city/airport",
        "destination": "arrival city/airport",
        "departureDate": "YYYY-MM-DD format",
        "returnDate": "YYYY-MM-DD format (if round trip)",
        "passengers": number,
        "class": "economy|business|first",
        "tripType": "one-way|round-trip"
      }`;
    } else if (serviceType === ServiceType.BOTH) {
      systemPrompt = `Extract both flight and hotel search parameters from the user's message.
      Return a JSON object with these fields (only include if mentioned):
      {
        "origin": "departure city/airport",
        "destination": "arrival city/airport",
        "departureDate": "YYYY-MM-DD format",
        "returnDate": "YYYY-MM-DD format (if round trip)",
        "passengers": number,
        "class": "economy|business|first",
        "tripType": "one-way|round-trip",
        "checkInDate": "YYYY-MM-DD format (can be same as departureDate)",
        "checkOutDate": "YYYY-MM-DD format (can be same as returnDate)",
        "guests": number,
        "rooms": number,
        "priceRange": "budget|mid-range|luxury",
        "amenities": ["wifi", "pool", "gym", etc.]
      }

      Smart suggestions:
      - If only flight dates provided, suggest checkInDate = departureDate and checkOutDate = returnDate
      - If only passenger count provided, suggest guests = passengers
      - If destination city mentioned, use it for both flight destination and hotel destination`;
    } else {
      return {};
    }

    try {
      const response = await this.bedrockService.invokeModel(message, state.conversationHistory, systemPrompt);
      const extracted = JSON.parse(response);

      // Apply smart suggestions for combined travel
      if (serviceType === ServiceType.BOTH) {
        extracted = this.applyCombinedTravelSuggestions(extracted);
      }

      return extracted;
    } catch (error) {
      logger.warn('Failed to extract parameters', { error: error.message });
      return {};
    }
  }

  /**
   * Get parameter collection prompt
   * @param {string} parameter - Missing parameter name
   * @param {string} serviceType - Service type
   * @returns {string} - System prompt
   */
  getParameterCollectionPrompt(parameter, serviceType) {
    const prompts = {
      hotel: {
        destination: 'Ask the user where they would like to stay (city or specific location).',
        checkInDate: 'Ask the user for their check-in date.',
        checkOutDate: 'Ask the user for their check-out date.',
        guests: 'Ask the user how many guests will be staying.',
        rooms: 'Ask the user how many rooms they need.'
      },
      flight: {
        origin: 'Ask the user where they will be departing from (city or airport).',
        destination: 'Ask the user where they want to fly to (city or airport).',
        departureDate: 'Ask the user for their departure date.',
        passengers: 'Ask the user how many passengers will be traveling.',
        returnDate: 'Ask the user if they need a return flight and when.'
      },
      both: {
        origin: 'Ask the user where they will be departing from (city or airport).',
        destination: 'Ask the user where they want to travel to (this will be used for both flight destination and hotel location).',
        departureDate: 'Ask the user for their departure date.',
        passengers: 'Ask the user how many passengers will be traveling.',
        returnDate: 'Ask the user if they need a return flight and when.',
        checkInDate: 'Ask the user for their hotel check-in date (mention it can be the same as their flight arrival date).',
        checkOutDate: 'Ask the user for their hotel check-out date (mention it can be the same as their return flight date).',
        guests: 'Ask the user how many guests will be staying at the hotel (mention it can be the same as the number of passengers).',
        rooms: 'Ask the user how many hotel rooms they need.'
      }
    };

    const servicePrompts = prompts[serviceType] || {};
    const specificPrompt = servicePrompts[parameter] || `Ask the user for their ${parameter}.`;

    return `You are a helpful travel assistant planning a complete trip. ${specificPrompt} Be friendly and conversational.`;
  }

  /**
   * Apply smart suggestions for combined travel planning
   * @param {Object} extracted - Extracted parameters
   * @returns {Object} - Parameters with smart suggestions applied
   */
  applyCombinedTravelSuggestions(extracted) {
    const suggestions = { ...extracted };

    // Suggest hotel dates based on flight dates
    if (extracted.departureDate && !extracted.checkInDate) {
      suggestions.checkInDate = extracted.departureDate;
    }
    if (extracted.returnDate && !extracted.checkOutDate) {
      suggestions.checkOutDate = extracted.returnDate;
    }

    // Suggest guest count based on passenger count
    if (extracted.passengers && !extracted.guests) {
      suggestions.guests = extracted.passengers;
    }

    // Use destination for both flight and hotel if only one mentioned
    if (extracted.destination && !suggestions.hotelDestination) {
      suggestions.hotelDestination = extracted.destination;
    }

    return suggestions;
  }

  /**
   * Get conversational system prompt that enables tool calling at any time
   * @param {ConversationState} state - Conversation state
   * @returns {string} - System prompt
   */
  getConversationalSystemPrompt(state) {
    return `You are a helpful travel assistant with access to powerful search tools.

    Available tools:
    - search_hotels: Search for hotel accommodations
    - search_flights: Search for flight options
    - search_combined_travel: Search for both flights and hotels for complete trip planning
    - get_hotel_details: Get detailed hotel information
    - get_flight_details: Get detailed flight information

    Current conversation context:
    - Service type: ${state.serviceType || 'Not determined'}
    - Collected parameters: ${JSON.stringify(state.collectedParameters)}
    - Conversation phase: ${state.phase}

    Instructions:
    1. If the user provides enough information for a search, use the appropriate tool immediately
    2. If you need more information, ask clarifying questions
    3. Always prioritize calling tools when you have sufficient information
    4. For complete trip planning, use search_combined_travel
    5. For specific details about hotels/flights, use the detail tools
    6. Provide helpful suggestions and cross-service recommendations

    Remember: You can call tools at any time when you have enough information. Don't wait for all parameters to be collected manually.`;
  }

  /**
   * Get system prompt for search operations (legacy - kept for compatibility)
   * @param {ConversationState} state - Conversation state
   * @returns {string} - System prompt
   */
  getSearchSystemPrompt(state) {
    const serviceType = state.serviceType;
    const parameters = state.collectedParameters;

    if (serviceType === 'both') {
      return `You are a helpful travel assistant. The user wants to plan a complete trip with both flights and hotels.

      Use the search_combined_travel tool to search for both flights and hotels simultaneously.

      Search parameters:
      - Origin: ${parameters.origin}
      - Destination: ${parameters.destination}
      - Departure Date: ${parameters.departureDate}
      - Return Date: ${parameters.returnDate || 'Not specified'}
      - Passengers: ${parameters.passengers}
      - Check-in Date: ${parameters.checkInDate || parameters.departureDate}
      - Check-out Date: ${parameters.checkOutDate || parameters.returnDate || parameters.departureDate}
      - Rooms: ${parameters.rooms || Math.ceil(parameters.passengers / 2)}
      - Flight Class: ${parameters.class || 'economy'}
      - Hotel Price Range: ${parameters.priceRange || 'Not specified'}

      After getting the results, present them in a friendly, organized way with smart suggestions for the best combinations.`;
    } else if (serviceType === 'hotel') {
      return `You are a helpful travel assistant. The user is looking for hotel accommodations.

      Use the search_hotels tool to find hotels based on their requirements.

      Search parameters:
      - Destination: ${parameters.destination}
      - Check-in Date: ${parameters.checkInDate}
      - Check-out Date: ${parameters.checkOutDate}
      - Guests: ${parameters.guests}
      - Rooms: ${parameters.rooms || 1}
      - Price Range: ${parameters.priceRange || 'Not specified'}
      - Amenities: ${parameters.amenities ? parameters.amenities.join(', ') : 'Not specified'}

      After getting the results, present them in a friendly way and ask if they also need flights to get there.`;
    } else if (serviceType === 'flight') {
      return `You are a helpful travel assistant. The user is looking for flight options.

      Use the search_flights tool to find flights based on their requirements.

      Search parameters:
      - Origin: ${parameters.origin}
      - Destination: ${parameters.destination}
      - Departure Date: ${parameters.departureDate}
      - Return Date: ${parameters.returnDate || 'Not specified'}
      - Passengers: ${parameters.passengers}
      - Class: ${parameters.class || 'economy'}
      - Trip Type: ${parameters.tripType || (parameters.returnDate ? 'round-trip' : 'one-way')}

      After getting the results, present them in a friendly way and ask if they also need accommodation at their destination.`;
    }

    return 'You are a helpful travel assistant. Use the appropriate search tools to help the user find what they need.';
  }

  /**
   * Format search message for the LLM
   * @param {ConversationState} state - Conversation state
   * @returns {string} - Formatted search message
   */
  formatSearchMessage(state) {
    const serviceType = state.serviceType;
    const parameters = state.collectedParameters;

    if (serviceType === 'both') {
      return `Please search for both flights and hotels for my trip from ${parameters.origin} to ${parameters.destination} on ${parameters.departureDate}${parameters.returnDate ? ` returning ${parameters.returnDate}` : ''} for ${parameters.passengers} passenger(s).`;
    } else if (serviceType === 'hotel') {
      return `Please search for hotels in ${parameters.destination} for ${parameters.guests} guest(s) from ${parameters.checkInDate} to ${parameters.checkOutDate}.`;
    } else if (serviceType === 'flight') {
      return `Please search for flights from ${parameters.origin} to ${parameters.destination} on ${parameters.departureDate}${parameters.returnDate ? ` returning ${parameters.returnDate}` : ''} for ${parameters.passengers} passenger(s).`;
    }

    return 'Please help me with my travel search.';
  }

  /**
   * Handle general conversation (fallback)
   * @param {ConversationState} state - Conversation state
   * @param {string} message - User message
   * @returns {Promise<Object>} - Response object
   */
  async handleGeneral(state, message) {
    const systemPrompt = `You are a helpful travel assistant with access to hotel and flight search tools.

    Available tools:
    - search_hotels: Search for hotel accommodations
    - search_flights: Search for flight options
    - search_combined_travel: Search for both flights and hotels for complete trip planning
    - get_hotel_details: Get detailed information about a specific hotel
    - get_flight_details: Get detailed information about a specific flight

    If the user is asking about travel services, guide them towards using these tools.
    If they provide enough information, you can directly use the appropriate search tool.`;

    // Use LangChain with tools for general conversation too
    const result = await this.bedrockService.invokeModelWithTools(
      message,
      state.conversationHistory,
      systemPrompt
    );

    state.addMessage('user', message);
    state.addMessage('assistant', result.response);

    // Update state if tools were called
    if (result.hasToolCalls) {
      state.update({
        phase: ConversationPhase.RESULT_PROCESSING,
        lastApiResponse: result.toolResults
      });
    }

    return {
      state,
      userMessage: message,
      response: result.response,
      toolCalls: result.toolCalls,
      toolResults: result.toolResults
    };
  }
}

export default ConversationManager;
