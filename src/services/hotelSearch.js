import axios from 'axios';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

/**
 * Hotel Search Service
 * Handles hotel search API calls and data processing
 */
class HotelSearchService {
  constructor() {
    this.apiKey = config.travelApis.hotel.apiKey;
    this.baseUrl = config.travelApis.hotel.baseUrl;
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 seconds timeout
    });
  }

  /**
   * Search for hotels
   * @param {Object} parameters - Search parameters
   * @returns {Promise<Object>} - Search results
   */
  async search(parameters) {
    try {
      logger.info('Hotel search initiated', { parameters });

      // Validate required parameters
      this.validateSearchParameters(parameters);

      // For demo purposes, we'll return mock data
      // In production, replace this with actual API call
      if (!this.apiKey || this.apiKey === 'your_hotel_api_key_here') {
        return this.getMockHotelResults(parameters);
      }

      // Actual API call (uncomment and modify for real API)
      /*
      const response = await this.client.post('/search', {
        destination: parameters.destination,
        check_in: parameters.checkInDate,
        check_out: parameters.checkOutDate,
        guests: parameters.guests,
        rooms: parameters.rooms || 1,
        price_range: parameters.priceRange,
        amenities: parameters.amenities || []
      });

      return this.processApiResponse(response.data);
      */

      return this.getMockHotelResults(parameters);
    } catch (error) {
      logger.error('Hotel search failed', { 
        error: error.message, 
        parameters,
        stack: error.stack 
      });
      throw new Error(`Hotel search failed: ${error.message}`);
    }
  }

  /**
   * Get hotel details by ID
   * @param {string} hotelId - Hotel identifier
   * @returns {Promise<Object>} - Hotel details
   */
  async getDetails(hotelId) {
    try {
      logger.info('Getting hotel details', { hotelId });

      // For demo purposes, return mock data
      if (!this.apiKey || this.apiKey === 'your_hotel_api_key_here') {
        return this.getMockHotelDetails(hotelId);
      }

      // Actual API call (uncomment and modify for real API)
      /*
      const response = await this.client.get(`/hotels/${hotelId}`);
      return this.processHotelDetails(response.data);
      */

      return this.getMockHotelDetails(hotelId);
    } catch (error) {
      logger.error('Failed to get hotel details', { 
        error: error.message, 
        hotelId,
        stack: error.stack 
      });
      throw new Error(`Failed to get hotel details: ${error.message}`);
    }
  }

  /**
   * Validate search parameters
   * @param {Object} parameters - Search parameters
   * @throws {Error} - If validation fails
   */
  validateSearchParameters(parameters) {
    const required = ['destination', 'checkInDate', 'checkOutDate', 'guests'];
    const missing = required.filter(field => !parameters[field]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required parameters: ${missing.join(', ')}`);
    }

    // Validate dates
    const checkIn = new Date(parameters.checkInDate);
    const checkOut = new Date(parameters.checkOutDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (checkIn < today) {
      throw new Error('Check-in date cannot be in the past');
    }

    if (checkOut <= checkIn) {
      throw new Error('Check-out date must be after check-in date');
    }

    // Validate guests
    if (parameters.guests < 1 || parameters.guests > 20) {
      throw new Error('Number of guests must be between 1 and 20');
    }
  }

  /**
   * Generate mock hotel search results for demo
   * @param {Object} parameters - Search parameters
   * @returns {Object} - Mock search results
   */
  getMockHotelResults(parameters) {
    const hotels = [
      {
        id: 'hotel_001',
        name: 'Grand Plaza Hotel',
        location: parameters.destination,
        rating: 4.5,
        pricePerNight: 150,
        currency: 'USD',
        amenities: ['WiFi', 'Pool', 'Gym', 'Restaurant', 'Spa'],
        images: ['https://example.com/hotel1.jpg'],
        description: 'Luxury hotel in the heart of the city with excellent amenities.',
        availability: true,
        totalPrice: this.calculateTotalPrice(150, parameters.checkInDate, parameters.checkOutDate)
      },
      {
        id: 'hotel_002',
        name: 'City Center Inn',
        location: parameters.destination,
        rating: 4.0,
        pricePerNight: 95,
        currency: 'USD',
        amenities: ['WiFi', 'Restaurant', 'Business Center'],
        images: ['https://example.com/hotel2.jpg'],
        description: 'Comfortable accommodation with modern facilities.',
        availability: true,
        totalPrice: this.calculateTotalPrice(95, parameters.checkInDate, parameters.checkOutDate)
      },
      {
        id: 'hotel_003',
        name: 'Budget Stay Lodge',
        location: parameters.destination,
        rating: 3.5,
        pricePerNight: 65,
        currency: 'USD',
        amenities: ['WiFi', 'Parking'],
        images: ['https://example.com/hotel3.jpg'],
        description: 'Affordable accommodation with basic amenities.',
        availability: true,
        totalPrice: this.calculateTotalPrice(65, parameters.checkInDate, parameters.checkOutDate)
      }
    ];

    // Filter by price range if specified
    let filteredHotels = hotels;
    if (parameters.priceRange) {
      filteredHotels = this.filterByPriceRange(hotels, parameters.priceRange);
    }

    return {
      hotels: filteredHotels,
      searchParameters: parameters,
      totalResults: filteredHotels.length,
      searchId: `search_${Date.now()}`,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Generate mock hotel details for demo
   * @param {string} hotelId - Hotel ID
   * @returns {Object} - Mock hotel details
   */
  getMockHotelDetails(hotelId) {
    return {
      id: hotelId,
      name: 'Grand Plaza Hotel',
      rating: 4.5,
      address: '123 Main Street, City Center',
      phone: '******-0123',
      email: '<EMAIL>',
      amenities: ['WiFi', 'Pool', 'Gym', 'Restaurant', 'Spa', 'Concierge'],
      roomTypes: [
        {
          type: 'Standard Room',
          price: 150,
          capacity: 2,
          amenities: ['WiFi', 'TV', 'Air Conditioning']
        },
        {
          type: 'Deluxe Suite',
          price: 250,
          capacity: 4,
          amenities: ['WiFi', 'TV', 'Air Conditioning', 'Kitchenette', 'Balcony']
        }
      ],
      images: [
        'https://example.com/hotel-exterior.jpg',
        'https://example.com/hotel-lobby.jpg',
        'https://example.com/hotel-room.jpg'
      ],
      description: 'A luxury hotel offering exceptional service and amenities in the heart of the city.',
      policies: {
        checkIn: '3:00 PM',
        checkOut: '11:00 AM',
        cancellation: 'Free cancellation up to 24 hours before check-in',
        pets: 'Pets allowed with additional fee'
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Calculate total price for stay duration
   * @param {number} pricePerNight - Price per night
   * @param {string} checkInDate - Check-in date
   * @param {string} checkOutDate - Check-out date
   * @returns {number} - Total price
   */
  calculateTotalPrice(pricePerNight, checkInDate, checkOutDate) {
    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);
    const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
    return pricePerNight * nights;
  }

  /**
   * Filter hotels by price range
   * @param {Array} hotels - Hotel list
   * @param {string} priceRange - Price range (budget|mid-range|luxury)
   * @returns {Array} - Filtered hotels
   */
  filterByPriceRange(hotels, priceRange) {
    const ranges = {
      budget: { min: 0, max: 100 },
      'mid-range': { min: 100, max: 200 },
      luxury: { min: 200, max: Infinity }
    };

    const range = ranges[priceRange];
    if (!range) return hotels;

    return hotels.filter(hotel => 
      hotel.pricePerNight >= range.min && hotel.pricePerNight < range.max
    );
  }
}

export default HotelSearchService;
