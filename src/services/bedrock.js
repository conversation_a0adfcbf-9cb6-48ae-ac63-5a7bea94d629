import { BedrockRuntimeClient, InvokeModelCommand } from '@aws-sdk/client-bedrock-runtime';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

class BedrockService {
  constructor() {
    this.client = new BedrockRuntimeClient({
      region: config.aws.region,
      credentials: {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey,
      },
    });
  }

  /**
   * Invoke Claude model with a message
   * @param {string} message - The user message
   * @param {Array} conversationHistory - Previous conversation history
   * @param {string} systemPrompt - System prompt for the model
   * @returns {Promise<string>} - The model's response
   */
  async invokeModel(message, conversationHistory = [], systemPrompt = '') {
    try {
      // Format messages for Claude
      const messages = this.formatMessages(message, conversationHistory);
      
      const requestBody = {
        anthropic_version: 'bedrock-2023-05-31',
        max_tokens: config.bedrock.maxTokens,
        temperature: config.bedrock.temperature,
        system: systemPrompt,
        messages: messages,
      };

      const command = new InvokeModelCommand({
        modelId: config.bedrock.modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(requestBody),
      });

      logger.info('Invoking Bedrock model', { 
        modelId: config.bedrock.modelId,
        messageLength: message.length,
        historyLength: conversationHistory.length 
      });

      const response = await this.client.send(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));

      if (responseBody.content && responseBody.content.length > 0) {
        const assistantMessage = responseBody.content[0].text;
        logger.info('Received response from Bedrock', { 
          responseLength: assistantMessage.length 
        });
        return assistantMessage;
      } else {
        throw new Error('No content in Bedrock response');
      }
    } catch (error) {
      logger.error('Error invoking Bedrock model', { 
        error: error.message,
        stack: error.stack 
      });
      throw new Error(`Bedrock invocation failed: ${error.message}`);
    }
  }

  /**
   * Format messages for Claude API
   * @param {string} currentMessage - Current user message
   * @param {Array} history - Conversation history
   * @returns {Array} - Formatted messages array
   */
  formatMessages(currentMessage, history) {
    const messages = [];
    
    // Add conversation history
    for (const item of history) {
      if (item.role === 'user') {
        messages.push({
          role: 'user',
          content: item.content,
        });
      } else if (item.role === 'assistant') {
        messages.push({
          role: 'assistant',
          content: item.content,
        });
      }
    }
    
    // Add current message
    messages.push({
      role: 'user',
      content: currentMessage,
    });
    
    return messages;
  }

  /**
   * Test the Bedrock connection
   * @returns {Promise<boolean>} - True if connection is successful
   */
  async testConnection() {
    try {
      await this.invokeModel('Hello, can you confirm you are working?', [], 'You are a helpful assistant. Respond briefly.');
      logger.info('Bedrock connection test successful');
      return true;
    } catch (error) {
      logger.error('Bedrock connection test failed', { error: error.message });
      return false;
    }
  }
}

export default BedrockService;
