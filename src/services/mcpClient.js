import axios from 'axios';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

/**
 * MCP Client Service
 * Handles communication with the MCP server for API calls
 */
class MCPClient {
  constructor() {
    this.baseUrl = `http://localhost:${config.mcp.serverPort}`;
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  /**
   * Search for hotels via MCP
   * @param {Object} parameters - Hotel search parameters
   * @returns {Promise<Object>} - Search results
   */
  async searchHotels(parameters) {
    try {
      logger.info('MCP hotel search request', { parameters });

      const response = await this.client.post('/search/hotels', {
        parameters
      });

      if (response.data.success) {
        logger.info('MCP hotel search successful', { 
          resultCount: response.data.data.hotels?.length || 0 
        });
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Hotel search failed');
      }
    } catch (error) {
      logger.error('MCP hotel search failed', { 
        error: error.message,
        parameters,
        stack: error.stack 
      });
      throw new Error(`Hotel search failed: ${error.message}`);
    }
  }

  /**
   * Search for flights via MCP
   * @param {Object} parameters - Flight search parameters
   * @returns {Promise<Object>} - Search results
   */
  async searchFlights(parameters) {
    try {
      logger.info('MCP flight search request', { parameters });

      const response = await this.client.post('/search/flights', {
        parameters
      });

      if (response.data.success) {
        logger.info('MCP flight search successful', { 
          outboundCount: response.data.data.outboundFlights?.length || 0,
          returnCount: response.data.data.returnFlights?.length || 0 
        });
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Flight search failed');
      }
    } catch (error) {
      logger.error('MCP flight search failed', { 
        error: error.message,
        parameters,
        stack: error.stack 
      });
      throw new Error(`Flight search failed: ${error.message}`);
    }
  }

  /**
   * Get hotel details via MCP
   * @param {string} hotelId - Hotel ID
   * @returns {Promise<Object>} - Hotel details
   */
  async getHotelDetails(hotelId) {
    try {
      logger.info('MCP hotel details request', { hotelId });

      const response = await this.client.post('/execute-tool', {
        tool: 'get_hotel_details',
        parameters: { hotelId }
      });

      if (response.data.success) {
        logger.info('MCP hotel details successful', { hotelId });
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Failed to get hotel details');
      }
    } catch (error) {
      logger.error('MCP hotel details failed', { 
        error: error.message,
        hotelId,
        stack: error.stack 
      });
      throw new Error(`Failed to get hotel details: ${error.message}`);
    }
  }

  /**
   * Get flight details via MCP
   * @param {string} flightId - Flight ID
   * @returns {Promise<Object>} - Flight details
   */
  async getFlightDetails(flightId) {
    try {
      logger.info('MCP flight details request', { flightId });

      const response = await this.client.post('/execute-tool', {
        tool: 'get_flight_details',
        parameters: { flightId }
      });

      if (response.data.success) {
        logger.info('MCP flight details successful', { flightId });
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Failed to get flight details');
      }
    } catch (error) {
      logger.error('MCP flight details failed', { 
        error: error.message,
        flightId,
        stack: error.stack 
      });
      throw new Error(`Failed to get flight details: ${error.message}`);
    }
  }

  /**
   * Execute a generic tool via MCP
   * @param {string} tool - Tool name
   * @param {Object} parameters - Tool parameters
   * @returns {Promise<Object>} - Tool result
   */
  async executeTool(tool, parameters) {
    try {
      logger.info('MCP tool execution request', { tool, parameters });

      const response = await this.client.post('/execute-tool', {
        tool,
        parameters
      });

      if (response.data.success) {
        logger.info('MCP tool execution successful', { tool });
        return response.data.data;
      } else {
        throw new Error(response.data.error || `Tool execution failed: ${tool}`);
      }
    } catch (error) {
      logger.error('MCP tool execution failed', { 
        error: error.message,
        tool,
        parameters,
        stack: error.stack 
      });
      throw new Error(`Tool execution failed: ${error.message}`);
    }
  }

  /**
   * Get available tools from MCP server
   * @returns {Promise<Array>} - Available tools
   */
  async getAvailableTools() {
    try {
      logger.info('Getting available MCP tools');

      const response = await this.client.get('/tools');
      
      logger.info('MCP tools retrieved', { 
        toolCount: response.data.tools?.length || 0 
      });
      
      return response.data.tools || [];
    } catch (error) {
      logger.error('Failed to get MCP tools', { 
        error: error.message,
        stack: error.stack 
      });
      throw new Error(`Failed to get available tools: ${error.message}`);
    }
  }

  /**
   * Test MCP server connection
   * @returns {Promise<boolean>} - Connection status
   */
  async testConnection() {
    try {
      const response = await this.client.get('/health');
      const isHealthy = response.status === 200 && response.data.status === 'healthy';
      
      logger.info('MCP connection test', { 
        healthy: isHealthy,
        status: response.data.status 
      });
      
      return isHealthy;
    } catch (error) {
      logger.warn('MCP connection test failed', { error: error.message });
      return false;
    }
  }

  /**
   * Set custom timeout for requests
   * @param {number} timeout - Timeout in milliseconds
   */
  setTimeout(timeout) {
    this.client.defaults.timeout = timeout;
    logger.info('MCP client timeout updated', { timeout });
  }

  /**
   * Set custom base URL for MCP server
   * @param {string} baseUrl - Base URL
   */
  setBaseUrl(baseUrl) {
    this.baseUrl = baseUrl;
    this.client.defaults.baseURL = baseUrl;
    logger.info('MCP client base URL updated', { baseUrl });
  }
}

export default MCPClient;
