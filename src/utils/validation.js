import Joi from 'joi';

/**
 * Validation schemas and utilities
 */

// Chat message validation schema
export const chatMessageSchema = Joi.object({
  message: Joi.string()
    .required()
    .min(1)
    .max(2000)
    .trim()
    .messages({
      'string.empty': 'Message cannot be empty',
      'string.min': 'Message must be at least 1 character long',
      'string.max': 'Message cannot exceed 2000 characters',
      'any.required': 'Message is required'
    }),
  sessionId: Joi.string()
    .uuid()
    .optional()
    .messages({
      'string.guid': 'Session ID must be a valid UUID'
    })
});

// Hotel search parameters validation schema
export const hotelSearchSchema = Joi.object({
  destination: Joi.string()
    .required()
    .min(2)
    .max(100)
    .trim()
    .messages({
      'string.empty': 'Destination is required',
      'string.min': 'Destination must be at least 2 characters long',
      'string.max': 'Destination cannot exceed 100 characters',
      'any.required': 'Destination is required'
    }),
  checkInDate: Joi.date()
    .iso()
    .min('now')
    .required()
    .messages({
      'date.base': 'Check-in date must be a valid date',
      'date.format': 'Check-in date must be in ISO format (YYYY-MM-DD)',
      'date.min': 'Check-in date cannot be in the past',
      'any.required': 'Check-in date is required'
    }),
  checkOutDate: Joi.date()
    .iso()
    .min(Joi.ref('checkInDate'))
    .required()
    .messages({
      'date.base': 'Check-out date must be a valid date',
      'date.format': 'Check-out date must be in ISO format (YYYY-MM-DD)',
      'date.min': 'Check-out date must be after check-in date',
      'any.required': 'Check-out date is required'
    }),
  guests: Joi.number()
    .integer()
    .min(1)
    .max(20)
    .required()
    .messages({
      'number.base': 'Number of guests must be a number',
      'number.integer': 'Number of guests must be a whole number',
      'number.min': 'At least 1 guest is required',
      'number.max': 'Maximum 20 guests allowed',
      'any.required': 'Number of guests is required'
    }),
  rooms: Joi.number()
    .integer()
    .min(1)
    .max(10)
    .optional()
    .default(1)
    .messages({
      'number.base': 'Number of rooms must be a number',
      'number.integer': 'Number of rooms must be a whole number',
      'number.min': 'At least 1 room is required',
      'number.max': 'Maximum 10 rooms allowed'
    }),
  priceRange: Joi.string()
    .valid('budget', 'mid-range', 'luxury')
    .optional()
    .messages({
      'any.only': 'Price range must be one of: budget, mid-range, luxury'
    }),
  amenities: Joi.array()
    .items(Joi.string().trim())
    .optional()
    .messages({
      'array.base': 'Amenities must be an array of strings'
    })
});

// Flight search parameters validation schema
export const flightSearchSchema = Joi.object({
  origin: Joi.string()
    .required()
    .min(2)
    .max(100)
    .trim()
    .messages({
      'string.empty': 'Origin is required',
      'string.min': 'Origin must be at least 2 characters long',
      'string.max': 'Origin cannot exceed 100 characters',
      'any.required': 'Origin is required'
    }),
  destination: Joi.string()
    .required()
    .min(2)
    .max(100)
    .trim()
    .invalid(Joi.ref('origin'))
    .messages({
      'string.empty': 'Destination is required',
      'string.min': 'Destination must be at least 2 characters long',
      'string.max': 'Destination cannot exceed 100 characters',
      'any.invalid': 'Destination cannot be the same as origin',
      'any.required': 'Destination is required'
    }),
  departureDate: Joi.date()
    .iso()
    .min('now')
    .required()
    .messages({
      'date.base': 'Departure date must be a valid date',
      'date.format': 'Departure date must be in ISO format (YYYY-MM-DD)',
      'date.min': 'Departure date cannot be in the past',
      'any.required': 'Departure date is required'
    }),
  returnDate: Joi.date()
    .iso()
    .min(Joi.ref('departureDate'))
    .optional()
    .messages({
      'date.base': 'Return date must be a valid date',
      'date.format': 'Return date must be in ISO format (YYYY-MM-DD)',
      'date.min': 'Return date must be after departure date'
    }),
  passengers: Joi.number()
    .integer()
    .min(1)
    .max(9)
    .required()
    .messages({
      'number.base': 'Number of passengers must be a number',
      'number.integer': 'Number of passengers must be a whole number',
      'number.min': 'At least 1 passenger is required',
      'number.max': 'Maximum 9 passengers allowed',
      'any.required': 'Number of passengers is required'
    }),
  class: Joi.string()
    .valid('economy', 'business', 'first')
    .optional()
    .default('economy')
    .messages({
      'any.only': 'Class must be one of: economy, business, first'
    }),
  tripType: Joi.string()
    .valid('one-way', 'round-trip')
    .optional()
    .default('one-way')
    .messages({
      'any.only': 'Trip type must be one of: one-way, round-trip'
    })
});

// Session ID validation schema
export const sessionIdSchema = Joi.string()
  .uuid()
  .required()
  .messages({
    'string.guid': 'Session ID must be a valid UUID',
    'any.required': 'Session ID is required'
  });

/**
 * Validate data against a schema
 * @param {Object} data - Data to validate
 * @param {Object} schema - Joi schema
 * @returns {Object} - Validation result
 */
export function validateData(data, schema) {
  const result = schema.validate(data, {
    abortEarly: false,
    stripUnknown: true,
    convert: true
  });

  return {
    isValid: !result.error,
    data: result.value,
    errors: result.error ? result.error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    })) : []
  };
}

/**
 * Sanitize user input
 * @param {string} input - User input string
 * @returns {string} - Sanitized string
 */
export function sanitizeInput(input) {
  if (typeof input !== 'string') {
    return input;
  }

  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 2000); // Limit length
}

/**
 * Validate and sanitize chat message
 * @param {Object} messageData - Message data
 * @returns {Object} - Validation result
 */
export function validateChatMessage(messageData) {
  // First sanitize the message
  if (messageData.message) {
    messageData.message = sanitizeInput(messageData.message);
  }

  // Then validate
  return validateData(messageData, chatMessageSchema);
}

/**
 * Validate hotel search parameters
 * @param {Object} searchData - Search parameters
 * @returns {Object} - Validation result
 */
export function validateHotelSearch(searchData) {
  return validateData(searchData, hotelSearchSchema);
}

/**
 * Validate flight search parameters
 * @param {Object} searchData - Search parameters
 * @returns {Object} - Validation result
 */
export function validateFlightSearch(searchData) {
  return validateData(searchData, flightSearchSchema);
}

/**
 * Validate session ID
 * @param {string} sessionId - Session ID
 * @returns {Object} - Validation result
 */
export function validateSessionId(sessionId) {
  return validateData({ sessionId }, { sessionId: sessionIdSchema });
}
