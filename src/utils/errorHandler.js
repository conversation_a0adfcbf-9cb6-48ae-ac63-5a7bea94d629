import logger from './logger.js';
import { config } from '../config/index.js';

/**
 * Custom error classes
 */

export class ValidationError extends Error {
  constructor(message, errors = []) {
    super(message);
    this.name = 'ValidationError';
    this.status = 400;
    this.errors = errors;
  }
}

export class BedrockError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'BedrockError';
    this.status = 503;
    this.originalError = originalError;
  }
}

export class MCPError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'MCPError';
    this.status = 503;
    this.originalError = originalError;
  }
}

export class ConversationError extends Error {
  constructor(message, sessionId = null) {
    super(message);
    this.name = 'ConversationError';
    this.status = 400;
    this.sessionId = sessionId;
  }
}

export class RateLimitError extends Error {
  constructor(message = 'Rate limit exceeded') {
    super(message);
    this.name = 'RateLimitError';
    this.status = 429;
  }
}

/**
 * Error handler utility functions
 */

/**
 * Handle and format errors for API responses
 * @param {Error} error - The error to handle
 * @param {Object} context - Additional context information
 * @returns {Object} - Formatted error response
 */
export function handleError(error, context = {}) {
  const errorId = generateErrorId();
  
  // Log the error with context
  logger.error('Error occurred', {
    errorId,
    name: error.name,
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  });

  // Determine error type and create appropriate response
  let response = {
    success: false,
    errorId,
    timestamp: new Date().toISOString()
  };

  switch (error.name) {
    case 'ValidationError':
      response = {
        ...response,
        error: 'Validation failed',
        message: error.message,
        details: error.errors,
        status: 400
      };
      break;

    case 'BedrockError':
      response = {
        ...response,
        error: 'AI service unavailable',
        message: config.nodeEnv === 'production' 
          ? 'The AI service is temporarily unavailable. Please try again later.'
          : error.message,
        status: 503
      };
      break;

    case 'MCPError':
      response = {
        ...response,
        error: 'Search service unavailable',
        message: config.nodeEnv === 'production'
          ? 'The search service is temporarily unavailable. Please try again later.'
          : error.message,
        status: 503
      };
      break;

    case 'ConversationError':
      response = {
        ...response,
        error: 'Conversation error',
        message: error.message,
        sessionId: error.sessionId,
        status: 400
      };
      break;

    case 'RateLimitError':
      response = {
        ...response,
        error: 'Rate limit exceeded',
        message: 'Too many requests. Please wait before trying again.',
        status: 429
      };
      break;

    default:
      // Generic error handling
      response = {
        ...response,
        error: 'Internal server error',
        message: config.nodeEnv === 'production'
          ? 'An unexpected error occurred. Please try again later.'
          : error.message,
        status: error.status || 500
      };
      
      // Include stack trace in development
      if (config.nodeEnv !== 'production') {
        response.stack = error.stack;
      }
      break;
  }

  return response;
}

/**
 * Wrap async functions with error handling
 * @param {Function} fn - Async function to wrap
 * @returns {Function} - Wrapped function
 */
export function asyncErrorHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Create Express error handling middleware
 * @returns {Function} - Express error middleware
 */
export function createErrorMiddleware() {
  return (error, req, res, next) => {
    const context = {
      method: req.method,
      path: req.path,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    };

    const errorResponse = handleError(error, context);
    res.status(errorResponse.status).json(errorResponse);
  };
}

/**
 * Retry mechanism for operations that might fail temporarily
 * @param {Function} operation - Operation to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} delay - Delay between retries in milliseconds
 * @returns {Promise} - Operation result
 */
export async function retryOperation(operation, maxRetries = 3, delay = 1000) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      logger.warn('Operation failed, retrying', {
        attempt,
        maxRetries,
        error: error.message,
        nextRetryIn: attempt < maxRetries ? `${delay}ms` : 'no more retries'
      });

      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }
  }
  
  throw lastError;
}

/**
 * Circuit breaker pattern implementation
 */
export class CircuitBreaker {
  constructor(options = {}) {
    this.failureThreshold = options.failureThreshold || 5;
    this.resetTimeout = options.resetTimeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 10000; // 10 seconds
    
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
  }

  async execute(operation) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime >= this.resetTimeout) {
        this.state = 'HALF_OPEN';
        this.successCount = 0;
        logger.info('Circuit breaker transitioning to HALF_OPEN');
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  onSuccess() {
    this.failureCount = 0;
    
    if (this.state === 'HALF_OPEN') {
      this.successCount++;
      if (this.successCount >= 3) { // Require 3 successes to close
        this.state = 'CLOSED';
        logger.info('Circuit breaker CLOSED');
      }
    }
  }

  onFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
      logger.warn('Circuit breaker OPEN', {
        failureCount: this.failureCount,
        threshold: this.failureThreshold
      });
    }
  }

  getState() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      successCount: this.successCount
    };
  }
}

/**
 * Generate unique error ID for tracking
 * @returns {string} - Unique error ID
 */
function generateErrorId() {
  return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Graceful degradation helper
 * @param {Function} primaryOperation - Primary operation to try
 * @param {Function} fallbackOperation - Fallback operation
 * @param {string} operationName - Name for logging
 * @returns {Promise} - Operation result
 */
export async function gracefulDegrade(primaryOperation, fallbackOperation, operationName) {
  try {
    return await primaryOperation();
  } catch (error) {
    logger.warn(`Primary operation failed, using fallback`, {
      operation: operationName,
      error: error.message
    });
    
    try {
      return await fallbackOperation();
    } catch (fallbackError) {
      logger.error(`Both primary and fallback operations failed`, {
        operation: operationName,
        primaryError: error.message,
        fallbackError: fallbackError.message
      });
      throw error; // Throw original error
    }
  }
}
