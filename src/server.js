import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from './config/index.js';
import logger from './utils/logger.js';
import ChatController from './controllers/chatController.js';
import MCPServer from './mcp/mcpServer.js';

/**
 * Main Express Server
 * Handles HTTP requests and coordinates with MCP server
 */
class Server {
  constructor() {
    this.app = express();
    this.chatController = new ChatController();
    this.mcpServer = new MCPServer();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Setup Express middleware
   */
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: process.env.NODE_ENV === 'production' 
        ? ['https://yourdomain.com'] // Replace with your frontend domain
        : ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging middleware
    this.app.use((req, res, next) => {
      logger.info('HTTP Request', {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });
      next();
    });

    // Response time middleware
    this.app.use((req, res, next) => {
      const start = Date.now();
      res.on('finish', () => {
        const duration = Date.now() - start;
        logger.info('HTTP Response', {
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
          duration: `${duration}ms`,
          timestamp: new Date().toISOString()
        });
      });
      next();
    });
  }

  /**
   * Setup API routes
   */
  setupRoutes() {
    // Health check endpoint
    this.app.get('/api/health', this.chatController.healthCheck.bind(this.chatController));

    // Chat endpoints
    this.app.post('/api/chat', this.chatController.handleChatMessage.bind(this.chatController));
    this.app.get('/api/chat/:sessionId/state', this.chatController.getConversationState.bind(this.chatController));
    this.app.post('/api/chat/:sessionId/reset', this.chatController.resetConversation.bind(this.chatController));

    // API documentation endpoint
    this.app.get('/api/docs', (req, res) => {
      res.json({
        title: 'LLM Travel Chatbot API',
        version: '1.0.0',
        description: 'API for LLM-powered travel chatbot with hotel and flight search capabilities',
        endpoints: {
          'GET /api/health': 'Health check endpoint',
          'POST /api/chat': 'Send chat message',
          'GET /api/chat/:sessionId/state': 'Get conversation state',
          'POST /api/chat/:sessionId/reset': 'Reset conversation',
          'GET /api/docs': 'API documentation'
        },
        chatEndpoint: {
          method: 'POST',
          path: '/api/chat',
          body: {
            message: 'string (required) - User message',
            sessionId: 'string (optional) - Session UUID'
          },
          response: {
            success: 'boolean',
            sessionId: 'string',
            response: 'string - Assistant response',
            state: 'object - Conversation state',
            timestamp: 'string'
          }
        },
        examples: {
          chatRequest: {
            message: 'I need to find a hotel in New York',
            sessionId: '123e4567-e89b-12d3-a456-************'
          }
        }
      });
    });

    // Root endpoint
    this.app.get('/', (req, res) => {
      res.json({
        message: 'LLM Travel Chatbot API',
        version: '1.0.0',
        status: 'running',
        endpoints: {
          health: '/api/health',
          chat: '/api/chat',
          docs: '/api/docs'
        },
        timestamp: new Date().toISOString()
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        path: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
      });
    });
  }

  /**
   * Setup error handling middleware
   */
  setupErrorHandling() {
    // Global error handler
    this.app.use((error, req, res, next) => {
      logger.error('Unhandled error', {
        error: error.message,
        stack: error.stack,
        path: req.path,
        method: req.method,
        body: req.body
      });

      // Don't expose internal errors in production
      const message = config.nodeEnv === 'production' 
        ? 'Internal server error' 
        : error.message;

      res.status(error.status || 500).json({
        success: false,
        error: message,
        timestamp: new Date().toISOString(),
        ...(config.nodeEnv !== 'production' && { stack: error.stack })
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', {
        error: error.message,
        stack: error.stack
      });
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection', {
        reason: reason,
        promise: promise
      });
      process.exit(1);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      this.shutdown();
    });

    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      this.shutdown();
    });
  }

  /**
   * Start the server and MCP server
   * @returns {Promise<void>}
   */
  async start() {
    try {
      // Start MCP server first
      await this.mcpServer.start();
      logger.info('MCP Server started successfully');

      // Start main HTTP server
      this.server = this.app.listen(config.port, () => {
        logger.info(`Server started on port ${config.port}`, {
          environment: config.nodeEnv,
          port: config.port,
          mcpPort: config.mcp.serverPort
        });
      });

      return this.server;
    } catch (error) {
      logger.error('Failed to start server', { error: error.message });
      throw error;
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    try {
      logger.info('Starting graceful shutdown...');

      // Close HTTP server
      if (this.server) {
        await new Promise((resolve) => {
          this.server.close(resolve);
        });
        logger.info('HTTP server closed');
      }

      // Close MCP server
      await this.mcpServer.stop();
      logger.info('MCP server closed');

      logger.info('Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown', { error: error.message });
      process.exit(1);
    }
  }
}

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new Server();
  server.start().catch((error) => {
    logger.error('Failed to start application', { error: error.message });
    process.exit(1);
  });
}

export default Server;
