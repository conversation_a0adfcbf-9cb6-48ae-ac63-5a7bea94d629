/**
 * Conversation state model for travel chatbot
 */

export const ConversationPhase = {
  GREETING: 'greeting',
  SERVICE_SELECTION: 'service_selection',
  HOTEL_SEARCH: 'hotel_search',
  FLIGHT_SEARCH: 'flight_search',
  PARAMETER_COLLECTION: 'parameter_collection',
  API_CALL: 'api_call',
  RESULT_PROCESSING: 'result_processing',
  FOLLOW_UP: 'follow_up',
  COMPLETED: 'completed',
};

export const ServiceType = {
  HOTEL: 'hotel',
  FLIGHT: 'flight',
  BOTH: 'both',
  UNKNOWN: 'unknown',
};

export class ConversationState {
  constructor() {
    this.sessionId = null;
    this.phase = ConversationPhase.GREETING;
    this.serviceType = ServiceType.UNKNOWN;
    this.userIntent = null;
    this.collectedParameters = {};
    this.requiredParameters = [];
    this.missingParameters = [];
    this.conversationHistory = [];
    this.lastApiResponse = null;
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Update the conversation state
   * @param {Object} updates - Updates to apply to the state
   */
  update(updates) {
    Object.assign(this, updates);
    this.updatedAt = new Date();
  }

  /**
   * Add a message to conversation history
   * @param {string} role - 'user' or 'assistant'
   * @param {string} content - Message content
   */
  addMessage(role, content) {
    this.conversationHistory.push({
      role,
      content,
      timestamp: new Date(),
    });
    this.updatedAt = new Date();
  }

  /**
   * Set required parameters based on service type
   * @param {string} serviceType - The service type (hotel/flight/both)
   */
  setRequiredParameters(serviceType) {
    this.serviceType = serviceType;

    if (serviceType === ServiceType.HOTEL) {
      this.requiredParameters = [
        'destination',
        'checkInDate',
        'checkOutDate',
        'guests',
      ];
    } else if (serviceType === ServiceType.FLIGHT) {
      this.requiredParameters = [
        'origin',
        'destination',
        'departureDate',
        'passengers',
      ];
    } else if (serviceType === ServiceType.BOTH) {
      this.requiredParameters = [
        'origin',
        'destination',
        'departureDate',
        'passengers',
        'checkInDate',
        'checkOutDate',
        'guests',
      ];
    }

    this.updateMissingParameters();
  }

  /**
   * Update missing parameters list
   */
  updateMissingParameters() {
    this.missingParameters = this.requiredParameters.filter(
      param => !this.collectedParameters[param] ||
        this.collectedParameters[param] === null ||
        this.collectedParameters[param] === undefined ||
        this.collectedParameters[param] === ''
    );
  }

  /**
   * Add or update a collected parameter
   * @param {string} key - Parameter key
   * @param {any} value - Parameter value
   */
  setParameter(key, value) {
    this.collectedParameters[key] = value;
    this.updateMissingParameters();
    this.updatedAt = new Date();
  }

  /**
   * Check if all required parameters are collected
   * @returns {boolean}
   */
  hasAllRequiredParameters() {
    return this.missingParameters.length === 0;
  }

  /**
   * Get the next missing parameter to collect
   * @returns {string|null}
   */
  getNextMissingParameter() {
    return this.missingParameters.length > 0 ? this.missingParameters[0] : null;
  }

  /**
   * Reset the conversation state
   */
  reset() {
    this.phase = ConversationPhase.GREETING;
    this.serviceType = ServiceType.UNKNOWN;
    this.userIntent = null;
    this.collectedParameters = {};
    this.requiredParameters = [];
    this.missingParameters = [];
    this.conversationHistory = [];
    this.lastApiResponse = null;
    this.updatedAt = new Date();
  }

  /**
   * Get a summary of the current state
   * @returns {Object}
   */
  getSummary() {
    return {
      sessionId: this.sessionId,
      phase: this.phase,
      serviceType: this.serviceType,
      collectedParameters: this.collectedParameters,
      missingParameters: this.missingParameters,
      hasAllParameters: this.hasAllRequiredParameters(),
      messageCount: this.conversationHistory.length,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  /**
   * Convert state to JSON
   * @returns {Object}
   */
  toJSON() {
    return {
      sessionId: this.sessionId,
      phase: this.phase,
      serviceType: this.serviceType,
      userIntent: this.userIntent,
      collectedParameters: this.collectedParameters,
      requiredParameters: this.requiredParameters,
      missingParameters: this.missingParameters,
      conversationHistory: this.conversationHistory,
      lastApiResponse: this.lastApiResponse,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  /**
   * Create state from JSON
   * @param {Object} json - JSON representation of state
   * @returns {ConversationState}
   */
  static fromJSON(json) {
    const state = new ConversationState();
    Object.assign(state, json);
    state.createdAt = new Date(json.createdAt);
    state.updatedAt = new Date(json.updatedAt);
    return state;
  }
}
