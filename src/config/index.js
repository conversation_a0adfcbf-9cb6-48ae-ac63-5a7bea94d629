import dotenv from 'dotenv';

dotenv.config();

export const config = {
  // Server Configuration
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // AWS Configuration
  aws: {
    region: process.env.AWS_REGION || 'us-east-1',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
  
  // Bedrock Configuration
  bedrock: {
    modelId: process.env.BEDROCK_MODEL_ID || 'anthropic.claude-3-5-sonnet-20241022-v2:0',
    maxTokens: 4096,
    temperature: 0.7,
  },
  
  // Travel API Configuration
  travelApis: {
    hotel: {
      apiKey: process.env.HOTEL_API_KEY,
      baseUrl: process.env.HOTEL_API_BASE_URL,
    },
    flight: {
      apiKey: process.env.FLIGHT_API_KEY,
      baseUrl: process.env.FLIGHT_API_BASE_URL,
    },
  },
  
  // MCP Configuration
  mcp: {
    serverPort: process.env.MCP_SERVER_PORT || 3001,
  },
  
  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
  },
  
  // Conversation Configuration
  conversation: {
    maxHistoryLength: 20,
    sessionTimeoutMs: 30 * 60 * 1000, // 30 minutes
  },
};

// Validate required configuration
const requiredEnvVars = [
  'AWS_ACCESS_KEY_ID',
  'AWS_SECRET_ACCESS_KEY',
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('Missing required environment variables:', missingVars.join(', '));
  console.error('Please check your .env file');
  process.exit(1);
}
