#!/usr/bin/env node

/**
 * Startup script for LLM Travel Chatbot
 * Handles environment validation and graceful startup
 */

import { config } from './src/config/index.js';
import logger from './src/utils/logger.js';
import Server from './src/server.js';

/**
 * Validate environment and dependencies
 */
async function validateEnvironment() {
  logger.info('Validating environment...');

  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    throw new Error(`Node.js 18+ is required. Current version: ${nodeVersion}`);
  }

  // Check required environment variables
  const requiredVars = [
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_REGION'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // Validate AWS region
  const validRegions = [
    'us-east-1', 'us-west-2', 'eu-west-1', 'eu-central-1', 
    'ap-southeast-1', 'ap-northeast-1'
  ];
  
  if (!validRegions.includes(config.aws.region)) {
    logger.warn(`AWS region ${config.aws.region} may not support Bedrock. Recommended regions: ${validRegions.join(', ')}`);
  }

  logger.info('Environment validation completed');
}

/**
 * Display startup banner
 */
function displayBanner() {
  console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    LLM Travel Chatbot                        ║
║                                                              ║
║  🤖 Powered by Claude Sonnet 4 via AWS Bedrock              ║
║  🔄 LangChain/LangGraph for conversation management          ║
║  🌐 MCP for real-time API integration                        ║
║                                                              ║
║  Environment: ${config.nodeEnv.padEnd(10)}                                    ║
║  Port: ${config.port.toString().padEnd(10)}                                         ║
║  MCP Port: ${config.mcp.serverPort.toString().padEnd(10)}                                    ║
╚══════════════════════════════════════════════════════════════╝
`);
}

/**
 * Display helpful information after startup
 */
function displayStartupInfo() {
  console.log(`
🚀 Server is running!

📋 API Endpoints:
   • Health Check: http://localhost:${config.port}/api/health
   • Chat API:     http://localhost:${config.port}/api/chat
   • Documentation: http://localhost:${config.port}/api/docs

🔧 MCP Server:
   • Health Check: http://localhost:${config.mcp.serverPort}/health
   • Tools List:   http://localhost:${config.mcp.serverPort}/tools

📖 Quick Start:
   • Test with: curl -X POST http://localhost:${config.port}/api/chat -H "Content-Type: application/json" -d '{"message":"Hello!"}'
   • Run examples: node examples/usage.js
   • View logs: tail -f logs/combined.log

💡 Tips:
   • Use the health endpoint to verify AWS Bedrock connectivity
   • Check logs/error.log for troubleshooting
   • The system uses mock data for travel APIs by default

Press Ctrl+C to stop the server
`);
}

/**
 * Main startup function
 */
async function main() {
  try {
    displayBanner();
    
    // Validate environment
    await validateEnvironment();
    
    // Create and start server
    logger.info('Starting LLM Travel Chatbot server...');
    const server = new Server();
    await server.start();
    
    // Display startup information
    displayStartupInfo();
    
    // Handle graceful shutdown
    const gracefulShutdown = async (signal) => {
      logger.info(`Received ${signal}, initiating graceful shutdown...`);
      await server.shutdown();
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
  } catch (error) {
    logger.error('Failed to start server', {
      error: error.message,
      stack: error.stack
    });
    
    console.error(`
❌ Startup failed: ${error.message}

🔧 Troubleshooting:
   • Check your .env file configuration
   • Verify AWS credentials and Bedrock access
   • Ensure ports ${config.port} and ${config.mcp.serverPort} are available
   • Check logs/error.log for detailed error information

📖 For help, see: README.md
`);
    
    process.exit(1);
  }
}

// Start the application
main().catch((error) => {
  console.error('Unhandled error during startup:', error);
  process.exit(1);
});
