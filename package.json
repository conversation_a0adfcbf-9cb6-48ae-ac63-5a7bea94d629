{"name": "llm-travel-chatbot", "version": "1.0.0", "description": "LLM-powered chatbot for hotel and flight search using Claude Sonnet 4, LangChain, and MCP", "main": "src/server.js", "type": "module", "scripts": {"start": "node start.js", "dev": "nodemon start.js", "test": "jest", "test:watch": "jest --watch", "example": "node examples/usage.js", "demo:langchain": "node examples/langchain-mcp-demo.js", "test:integration": "node examples/true-langchain-mcp.js"}, "keywords": ["llm", "chatbot", "claude", "bedrock", "langchain", "langgraph", "mcp", "travel", "hotel", "flight"], "author": "", "license": "MIT", "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.490.0", "@langchain/aws": "^0.0.8", "@langchain/core": "^0.1.52", "@langchain/community": "^0.0.43", "@langchain/langgraph": "^0.0.19", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "joi": "^17.11.0", "axios": "^1.6.2", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}