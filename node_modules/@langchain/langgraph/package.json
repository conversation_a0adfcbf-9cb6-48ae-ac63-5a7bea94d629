{"name": "@langchain/langgraph", "version": "0.0.19", "description": "LangGraph", "type": "module", "engines": {"node": ">=18"}, "main": "./index.js", "types": "./index.d.ts", "repository": {"type": "git", "url": "**************:langchain-ai/langgraphjs.git"}, "scripts": {"build": "yarn clean && yarn build:esm && yarn build:cjs && yarn build:scripts", "build:esm": "NODE_OPTIONS=--max-old-space-size=4096 rm -f src/package.json && tsc --outDir dist/", "build:cjs": "NODE_OPTIONS=--max-old-space-size=4096 echo '{}' > src/package.json && tsc --outDir dist-cjs/ -p tsconfig.cjs.json && yarn move-cjs-to-dist && rm -rf dist-cjs/ src/package.json", "build:watch": "yarn create-entrypoints && tsc --outDir dist/ --watch", "build:scripts": "yarn create-entrypoints && yarn check-tree-shaking", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint:dpdm": "dpdm --exit-code circular:1 --no-warning --no-tree src/*.ts src/**/*.ts", "lint": "yarn lint:eslint && yarn lint:dpdm", "lint:fix": "yarn lint:eslint --fix && yarn lint:dpdm", "clean": "rm -rf dist/ dist-cjs/ src/package.json && NODE_OPTIONS=--max-old-space-size=4096 yarn create-entrypoints -- --pre", "prepack": "yarn build", "test": "NODE_OPTIONS=--experimental-vm-modules jest --testPathIgnorePatterns=\\.int\\.test.ts --testTimeout 30000 --maxWorkers=50%", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest --watch --testPathIgnorePatterns=\\.int\\.test.ts", "test:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.cjs --testTimeout 100000", "test:int": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\"", "move-cjs-to-dist": "yarn lc-build --config ./langchain.config.js --move-cjs-dist", "create-entrypoints": "yarn lc-build --config ./langchain.config.js --create-entrypoints", "check-tree-shaking": "yarn lc-build --config ./langchain.config.js --tree-shaking"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@langchain/core": "^0.1.61", "uuid": "^9.0.1"}, "devDependencies": {"@jest/globals": "^29.5.0", "@langchain/anthropic": "^0.1.21", "@langchain/community": "^0.0.43", "@langchain/openai": "latest", "@langchain/scripts": "^0.0.13", "@swc/core": "^1.3.90", "@swc/jest": "^0.2.29", "@tsconfig/recommended": "^1.0.3", "@types/better-sqlite3": "^7.6.9", "@types/uuid": "^9", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "better-sqlite3": "^9.5.0", "dotenv": "^16.3.1", "dpdm": "^3.12.0", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "jest-environment-node": "^29.6.4", "langchain": "^0.1.29", "prettier": "^2.8.3", "release-it": "^15.10.1", "rollup": "^4.5.2", "ts-jest": "^29.1.0", "tsx": "^4.7.0", "typescript": "^5.4.5", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.4"}, "peerDependencies": {"better-sqlite3": "^9.5.0"}, "peerDependenciesMeta": {"better-sqlite3": {"optional": true}}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "exports": {".": {"types": {"import": "./index.d.ts", "require": "./index.d.cts", "default": "./index.d.ts"}, "import": "./index.js", "require": "./index.cjs"}, "./pregel": {"types": {"import": "./pregel.d.ts", "require": "./pregel.d.cts", "default": "./pregel.d.ts"}, "import": "./pregel.js", "require": "./pregel.cjs"}, "./prebuilt": {"types": {"import": "./prebuilt.d.ts", "require": "./prebuilt.d.cts", "default": "./prebuilt.d.ts"}, "import": "./prebuilt.js", "require": "./prebuilt.cjs"}, "./checkpoint/sqlite": {"types": {"import": "./checkpoint/sqlite.d.ts", "require": "./checkpoint/sqlite.d.cts", "default": "./checkpoint/sqlite.d.ts"}, "import": "./checkpoint/sqlite.js", "require": "./checkpoint/sqlite.cjs"}, "./package.json": "./package.json"}, "files": ["dist/", "index.cjs", "index.js", "index.d.ts", "index.d.cts", "pregel.cjs", "pregel.js", "pregel.d.ts", "pregel.d.cts", "prebuilt.cjs", "prebuilt.js", "prebuilt.d.ts", "prebuilt.d.cts", "checkpoint/sqlite.cjs", "checkpoint/sqlite.js", "checkpoint/sqlite.d.ts", "checkpoint/sqlite.d.cts"]}