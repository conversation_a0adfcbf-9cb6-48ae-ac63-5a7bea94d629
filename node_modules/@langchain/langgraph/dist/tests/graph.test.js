import { describe, it, expect } from "@jest/globals";
import { StateGraph } from "../graph/state.js";
describe("State", () => {
    it("should validate a new node key correctly ", () => {
        const stateGraph = new StateGraph({
            channels: { existingStateAttributeKey: null },
        });
        expect(() => {
            stateGraph.addNode("existingStateAttributeKey", (_) => ({}));
        }).toThrow("existingStateAttributeKey");
        expect(() => {
            stateGraph.addNode("newNodeKey", (_) => ({}));
        }).not.toThrow();
    });
});
