import { Tool } from "@langchain/core/tools";
import { CallbackManagerForLLMRun } from "@langchain/core/callbacks/manager";
import { BaseChatModel } from "@langchain/core/language_models/chat_models";
import { BaseLLMParams } from "@langchain/core/language_models/llms";
import { BaseMessage } from "@langchain/core/messages";
import { ChatResult } from "@langchain/core/outputs";
export declare class FakeToolCallingChatModel extends BaseChatModel {
    sleep?: number;
    responses?: BaseMessage[];
    thrownErrorString?: string;
    idx: number;
    constructor(fields: {
        sleep?: number;
        responses?: BaseMessage[];
        thrownErrorString?: string;
    } & BaseLLMParams);
    _llmType(): string;
    _generate(messages: BaseMessage[], _options: this["ParsedCallOptions"], _runManager?: CallbackManagerForLLMRun): Promise<ChatResult>;
    bindTools(_: Tool[]): FakeToolCallingChatModel;
}
