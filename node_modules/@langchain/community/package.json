{"name": "@langchain/community", "version": "0.0.43", "description": "Third-party integrations for LangChain.js", "type": "module", "engines": {"node": ">=18"}, "main": "./index.js", "types": "./index.d.ts", "repository": {"type": "git", "url": "**************:langchain-ai/langchainjs.git"}, "homepage": "https://github.com/langchain-ai/langchainjs/tree/main/libs/langchain-community/", "scripts": {"build": "yarn run build:deps && yarn clean && yarn build:esm && yarn build:cjs && yarn build:scripts", "build:deps": "yarn run turbo:command build --filter=@langchain/core --filter=@langchain/anthropic --filter=@langchain/openai --concurrency=1", "build:esm": "NODE_OPTIONS=--max-old-space-size=4096 tsc --outDir dist/ && rm -rf dist/tests dist/**/tests", "build:cjs": "NODE_OPTIONS=--max-old-space-size=4096 tsc --outDir dist-cjs/ -p tsconfig.cjs.json && yarn move-cjs-to-dist && rm -rf dist-cjs", "build:watch": "yarn create-entrypoints && tsc --outDir dist/ --watch", "build:scripts": "yarn create-entrypoints && yarn check-tree-shaking", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint:dpdm": "dpdm --exit-code circular:1 --no-warning --no-tree src/*.ts src/**/*.ts", "lint": "yarn lint:eslint && yarn lint:dpdm", "lint:fix": "yarn lint:eslint --fix && yarn lint:dpdm", "clean": "rm -rf dist/ && NODE_OPTIONS=--max-old-space-size=4096 yarn lc-build --config ./langchain.config.js --create-entrypoints --pre", "prepack": "yarn build", "test": "yarn run build:deps && NODE_OPTIONS=--experimental-vm-modules jest --testPathIgnorePatterns=\\.int\\.test.ts --testTimeout 30000 --maxWorkers=50%", "test:watch": "yarn run build:deps && NODE_OPTIONS=--experimental-vm-modules jest --watch --testPathIgnorePatterns=\\.int\\.test.ts", "test:single": "yarn run build:deps && NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.cjs --testTimeout 100000", "test:integration": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\"", "move-cjs-to-dist": "yarn lc-build --config ./langchain.config.js --move-cjs-dist", "create-entrypoints": "yarn lc-build --config ./langchain.config.js --create-entrypoints", "check-tree-shaking": "yarn lc-build --config ./langchain.config.js --tree-shaking"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@langchain/core": "~0.1.44", "@langchain/openai": "~0.0.19", "expr-eval": "^2.0.2", "flat": "^5.0.2", "langsmith": "~0.1.1", "uuid": "^9.0.0", "zod": "^3.22.3"}, "devDependencies": {"@aws-crypto/sha256-js": "^5.0.0", "@aws-sdk/client-bedrock-agent-runtime": "^3.496.0", "@aws-sdk/client-bedrock-runtime": "^3.422.0", "@aws-sdk/client-dynamodb": "^3.310.0", "@aws-sdk/client-kendra": "^3.352.0", "@aws-sdk/client-lambda": "^3.310.0", "@aws-sdk/client-sagemaker-runtime": "^3.414.0", "@aws-sdk/client-sfn": "^3.362.0", "@aws-sdk/credential-provider-node": "^3.388.0", "@aws-sdk/types": "^3.357.0", "@azure/search-documents": "^12.0.0", "@clickhouse/client": "^0.2.5", "@cloudflare/ai": "^1.0.12", "@cloudflare/workers-types": "^4.20230922.0", "@datastax/astra-db-ts": "^0.1.4", "@elastic/elasticsearch": "^8.4.0", "@faker-js/faker": "^7.6.0", "@getmetal/metal-sdk": "^4.0.0", "@getzep/zep-js": "^0.9.0", "@gomomento/sdk": "^1.51.1", "@gomomento/sdk-core": "^1.51.1", "@google-ai/generativelanguage": "^0.2.1", "@gradientai/nodejs-sdk": "^1.2.0", "@huggingface/inference": "^2.6.4", "@jest/globals": "^29.5.0", "@langchain/scripts": "~0.0", "@mozilla/readability": "^0.4.4", "@opensearch-project/opensearch": "^2.2.0", "@pinecone-database/pinecone": "^1.1.0", "@planetscale/database": "^1.8.0", "@premai/prem-sdk": "^0.3.25", "@qdrant/js-client-rest": "^1.2.0", "@raycast/api": "^1.55.2", "@rockset/client": "^0.9.1", "@smithy/eventstream-codec": "^2.0.5", "@smithy/protocol-http": "^3.0.6", "@smithy/signature-v4": "^2.0.10", "@smithy/util-utf8": "^2.0.0", "@supabase/postgrest-js": "^1.1.1", "@supabase/supabase-js": "^2.10.0", "@swc/core": "^1.3.90", "@swc/jest": "^0.2.29", "@tensorflow-models/universal-sentence-encoder": "^1.3.3", "@tensorflow/tfjs-backend-cpu": "^3", "@tensorflow/tfjs-converter": "^3.6.0", "@tensorflow/tfjs-core": "^3.6.0", "@tsconfig/recommended": "^1.0.2", "@types/better-sqlite3": "^7.6.9", "@types/flat": "^5.0.2", "@types/html-to-text": "^9", "@types/jsdom": "^21.1.1", "@types/jsonwebtoken": "^9", "@types/lodash": "^4", "@types/mozilla-readability": "^0.2.1", "@types/pg": "^8.11.0", "@types/pg-copy-streams": "^1.2.2", "@types/uuid": "^9", "@types/ws": "^8", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@upstash/redis": "^1.20.6", "@upstash/vector": "^1.0.2", "@vercel/kv": "^0.2.3", "@vercel/postgres": "^0.5.0", "@writerai/writer-sdk": "^0.40.2", "@xata.io/client": "^0.28.0", "@xenova/transformers": "^2.5.4", "@zilliz/milvus2-sdk-node": ">=2.2.11", "better-sqlite3": "^9.4.0", "cassandra-driver": "^4.7.2", "cborg": "^4.1.1", "chromadb": "^1.5.3", "closevector-common": "0.1.3", "closevector-node": "0.1.6", "closevector-web": "0.1.6", "cohere-ai": ">=6.0.0", "convex": "^1.3.1", "couchbase": "^4.3.0", "datastore-core": "^9.2.9", "discord.js": "^14.14.1", "dotenv": "^16.0.3", "dpdm": "^3.12.0", "dria": "^0.0.3", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "faiss-node": "^0.5.1", "firebase-admin": "^11.9.0 || ^12.0.0", "google-auth-library": "^8.9.0", "googleapis": "^126.0.1", "graphql": "^16.6.0", "hnswlib-node": "^1.4.2", "html-to-text": "^9.0.5", "interface-datastore": "^8.2.11", "ioredis": "^5.3.2", "it-all": "^3.0.4", "jest": "^29.5.0", "jest-environment-node": "^29.6.4", "jsdom": "^22.1.0", "jsonwebtoken": "^9.0.2", "llmonitor": "^0.5.9", "lodash": "^4.17.21", "lunary": "^0.6.11", "mongodb": "^5.2.0", "mysql2": "^3.3.3", "neo4j-driver": "^5.17.0", "node-llama-cpp": "2.7.3", "pg": "^8.11.0", "pg-copy-streams": "^6.0.5", "pickleparser": "^0.2.1", "portkey-ai": "^0.1.11", "prettier": "^2.8.3", "redis": "^4.6.6", "release-it": "^15.10.1", "replicate": "^0.18.0", "rollup": "^3.19.1", "ts-jest": "^29.1.0", "typeorm": "^0.3.12", "typescript": "~5.1.6", "typesense": "^1.5.3", "usearch": "^1.1.1", "vectordb": "^0.1.4", "voy-search": "0.6.2", "weaviate-ts-client": "^1.4.0", "web-auth-library": "^1.0.3"}, "peerDependencies": {"@aws-crypto/sha256-js": "^5.0.0", "@aws-sdk/client-bedrock-agent-runtime": "^3.485.0", "@aws-sdk/client-bedrock-runtime": "^3.422.0", "@aws-sdk/client-dynamodb": "^3.310.0", "@aws-sdk/client-kendra": "^3.352.0", "@aws-sdk/client-lambda": "^3.310.0", "@aws-sdk/client-sagemaker-runtime": "^3.310.0", "@aws-sdk/client-sfn": "^3.310.0", "@aws-sdk/credential-provider-node": "^3.388.0", "@azure/search-documents": "^12.0.0", "@clickhouse/client": "^0.2.5", "@cloudflare/ai": "*", "@datastax/astra-db-ts": "^0.1.4", "@elastic/elasticsearch": "^8.4.0", "@getmetal/metal-sdk": "*", "@getzep/zep-js": "^0.9.0", "@gomomento/sdk": "^1.51.1", "@gomomento/sdk-core": "^1.51.1", "@google-ai/generativelanguage": "^0.2.1", "@gradientai/nodejs-sdk": "^1.2.0", "@huggingface/inference": "^2.6.4", "@mozilla/readability": "*", "@opensearch-project/opensearch": "*", "@pinecone-database/pinecone": "*", "@planetscale/database": "^1.8.0", "@premai/prem-sdk": "^0.3.25", "@qdrant/js-client-rest": "^1.2.0", "@raycast/api": "^1.55.2", "@rockset/client": "^0.9.1", "@smithy/eventstream-codec": "^2.0.5", "@smithy/protocol-http": "^3.0.6", "@smithy/signature-v4": "^2.0.10", "@smithy/util-utf8": "^2.0.0", "@supabase/postgrest-js": "^1.1.1", "@supabase/supabase-js": "^2.10.0", "@tensorflow-models/universal-sentence-encoder": "*", "@tensorflow/tfjs-converter": "*", "@tensorflow/tfjs-core": "*", "@upstash/redis": "^1.20.6", "@upstash/vector": "^1.0.2", "@vercel/kv": "^0.2.3", "@vercel/postgres": "^0.5.0", "@writerai/writer-sdk": "^0.40.2", "@xata.io/client": "^0.28.0", "@xenova/transformers": "^2.5.4", "@zilliz/milvus2-sdk-node": ">=2.2.7", "better-sqlite3": "^9.4.0", "cassandra-driver": "^4.7.2", "cborg": "^4.1.1", "chromadb": "*", "closevector-common": "0.1.3", "closevector-node": "0.1.6", "closevector-web": "0.1.6", "cohere-ai": "*", "convex": "^1.3.1", "couchbase": "^4.3.0", "discord.js": "^14.14.1", "dria": "^0.0.3", "faiss-node": "^0.5.1", "firebase-admin": "^11.9.0 || ^12.0.0", "google-auth-library": "^8.9.0", "googleapis": "^126.0.1", "hnswlib-node": "^1.4.2", "html-to-text": "^9.0.5", "interface-datastore": "^8.2.11", "ioredis": "^5.3.2", "it-all": "^3.0.4", "jsdom": "*", "jsonwebtoken": "^9.0.2", "llmonitor": "^0.5.9", "lodash": "^4.17.21", "lunary": "^0.6.11", "mongodb": ">=5.2.0", "mysql2": "^3.3.3", "neo4j-driver": "*", "node-llama-cpp": "*", "pg": "^8.11.0", "pg-copy-streams": "^6.0.5", "pickleparser": "^0.2.1", "portkey-ai": "^0.1.11", "redis": "*", "replicate": "^0.18.0", "typeorm": "^0.3.12", "typesense": "^1.5.3", "usearch": "^1.1.1", "vectordb": "^0.1.4", "voy-search": "0.6.2", "weaviate-ts-client": "*", "web-auth-library": "^1.0.3", "ws": "^8.14.2"}, "peerDependenciesMeta": {"@aws-crypto/sha256-js": {"optional": true}, "@aws-sdk/client-bedrock-agent-runtime": {"optional": true}, "@aws-sdk/client-bedrock-runtime": {"optional": true}, "@aws-sdk/client-dynamodb": {"optional": true}, "@aws-sdk/client-kendra": {"optional": true}, "@aws-sdk/client-lambda": {"optional": true}, "@aws-sdk/client-sagemaker-runtime": {"optional": true}, "@aws-sdk/client-sfn": {"optional": true}, "@aws-sdk/credential-provider-node": {"optional": true}, "@azure/search-documents": {"optional": true}, "@clickhouse/client": {"optional": true}, "@cloudflare/ai": {"optional": true}, "@datastax/astra-db-ts": {"optional": true}, "@elastic/elasticsearch": {"optional": true}, "@getmetal/metal-sdk": {"optional": true}, "@getzep/zep-js": {"optional": true}, "@gomomento/sdk": {"optional": true}, "@gomomento/sdk-core": {"optional": true}, "@google-ai/generativelanguage": {"optional": true}, "@gradientai/nodejs-sdk": {"optional": true}, "@huggingface/inference": {"optional": true}, "@mozilla/readability": {"optional": true}, "@opensearch-project/opensearch": {"optional": true}, "@pinecone-database/pinecone": {"optional": true}, "@planetscale/database": {"optional": true}, "@premai/prem-sdk": {"optional": true}, "@qdrant/js-client-rest": {"optional": true}, "@raycast/api": {"optional": true}, "@rockset/client": {"optional": true}, "@smithy/eventstream-codec": {"optional": true}, "@smithy/protocol-http": {"optional": true}, "@smithy/signature-v4": {"optional": true}, "@smithy/util-utf8": {"optional": true}, "@supabase/postgrest-js": {"optional": true}, "@supabase/supabase-js": {"optional": true}, "@tensorflow-models/universal-sentence-encoder": {"optional": true}, "@tensorflow/tfjs-converter": {"optional": true}, "@tensorflow/tfjs-core": {"optional": true}, "@upstash/redis": {"optional": true}, "@upstash/vector": {"optional": true}, "@vercel/kv": {"optional": true}, "@vercel/postgres": {"optional": true}, "@writerai/writer-sdk": {"optional": true}, "@xata.io/client": {"optional": true}, "@xenova/transformers": {"optional": true}, "@zilliz/milvus2-sdk-node": {"optional": true}, "better-sqlite3": {"optional": true}, "cassandra-driver": {"optional": true}, "cborg": {"optional": true}, "chromadb": {"optional": true}, "closevector-common": {"optional": true}, "closevector-node": {"optional": true}, "closevector-web": {"optional": true}, "cohere-ai": {"optional": true}, "convex": {"optional": true}, "couchbase": {"optional": true}, "discord.js": {"optional": true}, "dria": {"optional": true}, "faiss-node": {"optional": true}, "firebase-admin": {"optional": true}, "google-auth-library": {"optional": true}, "googleapis": {"optional": true}, "hnswlib-node": {"optional": true}, "html-to-text": {"optional": true}, "interface-datastore": {"optional": true}, "ioredis": {"optional": true}, "it-all": {"optional": true}, "jsdom": {"optional": true}, "jsonwebtoken": {"optional": true}, "llmonitor": {"optional": true}, "lodash": {"optional": true}, "lunary": {"optional": true}, "mongodb": {"optional": true}, "mysql2": {"optional": true}, "neo4j-driver": {"optional": true}, "node-llama-cpp": {"optional": true}, "pg": {"optional": true}, "pg-copy-streams": {"optional": true}, "pickleparser": {"optional": true}, "portkey-ai": {"optional": true}, "redis": {"optional": true}, "replicate": {"optional": true}, "typeorm": {"optional": true}, "typesense": {"optional": true}, "usearch": {"optional": true}, "vectordb": {"optional": true}, "voy-search": {"optional": true}, "weaviate-ts-client": {"optional": true}, "web-auth-library": {"optional": true}, "ws": {"optional": true}}, "publishConfig": {"access": "public"}, "exports": {"./load": {"types": {"import": "./load.d.ts", "require": "./load.d.cts", "default": "./load.d.ts"}, "import": "./load.js", "require": "./load.cjs"}, "./load/serializable": {"types": {"import": "./load/serializable.d.ts", "require": "./load/serializable.d.cts", "default": "./load/serializable.d.ts"}, "import": "./load/serializable.js", "require": "./load/serializable.cjs"}, "./tools/aiplugin": {"types": {"import": "./tools/aiplugin.d.ts", "require": "./tools/aiplugin.d.cts", "default": "./tools/aiplugin.d.ts"}, "import": "./tools/aiplugin.js", "require": "./tools/aiplugin.cjs"}, "./tools/aws_lambda": {"types": {"import": "./tools/aws_lambda.d.ts", "require": "./tools/aws_lambda.d.cts", "default": "./tools/aws_lambda.d.ts"}, "import": "./tools/aws_lambda.js", "require": "./tools/aws_lambda.cjs"}, "./tools/aws_sfn": {"types": {"import": "./tools/aws_sfn.d.ts", "require": "./tools/aws_sfn.d.cts", "default": "./tools/aws_sfn.d.ts"}, "import": "./tools/aws_sfn.js", "require": "./tools/aws_sfn.cjs"}, "./tools/bingserpapi": {"types": {"import": "./tools/bingserpapi.d.ts", "require": "./tools/bingserpapi.d.cts", "default": "./tools/bingserpapi.d.ts"}, "import": "./tools/bingserpapi.js", "require": "./tools/bingserpapi.cjs"}, "./tools/brave_search": {"types": {"import": "./tools/brave_search.d.ts", "require": "./tools/brave_search.d.cts", "default": "./tools/brave_search.d.ts"}, "import": "./tools/brave_search.js", "require": "./tools/brave_search.cjs"}, "./tools/calculator": {"types": {"import": "./tools/calculator.d.ts", "require": "./tools/calculator.d.cts", "default": "./tools/calculator.d.ts"}, "import": "./tools/calculator.js", "require": "./tools/calculator.cjs"}, "./tools/connery": {"types": {"import": "./tools/connery.d.ts", "require": "./tools/connery.d.cts", "default": "./tools/connery.d.ts"}, "import": "./tools/connery.js", "require": "./tools/connery.cjs"}, "./tools/dadjokeapi": {"types": {"import": "./tools/dadjokeapi.d.ts", "require": "./tools/dadjokeapi.d.cts", "default": "./tools/dadjokeapi.d.ts"}, "import": "./tools/dadjokeapi.js", "require": "./tools/dadjokeapi.cjs"}, "./tools/discord": {"types": {"import": "./tools/discord.d.ts", "require": "./tools/discord.d.cts", "default": "./tools/discord.d.ts"}, "import": "./tools/discord.js", "require": "./tools/discord.cjs"}, "./tools/dynamic": {"types": {"import": "./tools/dynamic.d.ts", "require": "./tools/dynamic.d.cts", "default": "./tools/dynamic.d.ts"}, "import": "./tools/dynamic.js", "require": "./tools/dynamic.cjs"}, "./tools/dataforseo_api_search": {"types": {"import": "./tools/dataforseo_api_search.d.ts", "require": "./tools/dataforseo_api_search.d.cts", "default": "./tools/dataforseo_api_search.d.ts"}, "import": "./tools/dataforseo_api_search.js", "require": "./tools/dataforseo_api_search.cjs"}, "./tools/gmail": {"types": {"import": "./tools/gmail.d.ts", "require": "./tools/gmail.d.cts", "default": "./tools/gmail.d.ts"}, "import": "./tools/gmail.js", "require": "./tools/gmail.cjs"}, "./tools/google_calendar": {"types": {"import": "./tools/google_calendar.d.ts", "require": "./tools/google_calendar.d.cts", "default": "./tools/google_calendar.d.ts"}, "import": "./tools/google_calendar.js", "require": "./tools/google_calendar.cjs"}, "./tools/google_custom_search": {"types": {"import": "./tools/google_custom_search.d.ts", "require": "./tools/google_custom_search.d.cts", "default": "./tools/google_custom_search.d.ts"}, "import": "./tools/google_custom_search.js", "require": "./tools/google_custom_search.cjs"}, "./tools/google_places": {"types": {"import": "./tools/google_places.d.ts", "require": "./tools/google_places.d.cts", "default": "./tools/google_places.d.ts"}, "import": "./tools/google_places.js", "require": "./tools/google_places.cjs"}, "./tools/ifttt": {"types": {"import": "./tools/ifttt.d.ts", "require": "./tools/ifttt.d.cts", "default": "./tools/ifttt.d.ts"}, "import": "./tools/ifttt.js", "require": "./tools/ifttt.cjs"}, "./tools/searchapi": {"types": {"import": "./tools/searchapi.d.ts", "require": "./tools/searchapi.d.cts", "default": "./tools/searchapi.d.ts"}, "import": "./tools/searchapi.js", "require": "./tools/searchapi.cjs"}, "./tools/searxng_search": {"types": {"import": "./tools/searxng_search.d.ts", "require": "./tools/searxng_search.d.cts", "default": "./tools/searxng_search.d.ts"}, "import": "./tools/searxng_search.js", "require": "./tools/searxng_search.cjs"}, "./tools/serpapi": {"types": {"import": "./tools/serpapi.d.ts", "require": "./tools/serpapi.d.cts", "default": "./tools/serpapi.d.ts"}, "import": "./tools/serpapi.js", "require": "./tools/serpapi.cjs"}, "./tools/serper": {"types": {"import": "./tools/serper.d.ts", "require": "./tools/serper.d.cts", "default": "./tools/serper.d.ts"}, "import": "./tools/serper.js", "require": "./tools/serper.cjs"}, "./tools/tavily_search": {"types": {"import": "./tools/tavily_search.d.ts", "require": "./tools/tavily_search.d.cts", "default": "./tools/tavily_search.d.ts"}, "import": "./tools/tavily_search.js", "require": "./tools/tavily_search.cjs"}, "./tools/wikipedia_query_run": {"types": {"import": "./tools/wikipedia_query_run.d.ts", "require": "./tools/wikipedia_query_run.d.cts", "default": "./tools/wikipedia_query_run.d.ts"}, "import": "./tools/wikipedia_query_run.js", "require": "./tools/wikipedia_query_run.cjs"}, "./tools/wolframalpha": {"types": {"import": "./tools/wolframalpha.d.ts", "require": "./tools/wolframalpha.d.cts", "default": "./tools/wolframalpha.d.ts"}, "import": "./tools/wolframalpha.js", "require": "./tools/wolframalpha.cjs"}, "./agents/toolkits/aws_sfn": {"types": {"import": "./agents/toolkits/aws_sfn.d.ts", "require": "./agents/toolkits/aws_sfn.d.cts", "default": "./agents/toolkits/aws_sfn.d.ts"}, "import": "./agents/toolkits/aws_sfn.js", "require": "./agents/toolkits/aws_sfn.cjs"}, "./agents/toolkits/base": {"types": {"import": "./agents/toolkits/base.d.ts", "require": "./agents/toolkits/base.d.cts", "default": "./agents/toolkits/base.d.ts"}, "import": "./agents/toolkits/base.js", "require": "./agents/toolkits/base.cjs"}, "./agents/toolkits/connery": {"types": {"import": "./agents/toolkits/connery.d.ts", "require": "./agents/toolkits/connery.d.cts", "default": "./agents/toolkits/connery.d.ts"}, "import": "./agents/toolkits/connery.js", "require": "./agents/toolkits/connery.cjs"}, "./embeddings/alibaba_tongyi": {"types": {"import": "./embeddings/alibaba_tongyi.d.ts", "require": "./embeddings/alibaba_tongyi.d.cts", "default": "./embeddings/alibaba_tongyi.d.ts"}, "import": "./embeddings/alibaba_tongyi.js", "require": "./embeddings/alibaba_tongyi.cjs"}, "./embeddings/bedrock": {"types": {"import": "./embeddings/bedrock.d.ts", "require": "./embeddings/bedrock.d.cts", "default": "./embeddings/bedrock.d.ts"}, "import": "./embeddings/bedrock.js", "require": "./embeddings/bedrock.cjs"}, "./embeddings/cloudflare_workersai": {"types": {"import": "./embeddings/cloudflare_workersai.d.ts", "require": "./embeddings/cloudflare_workersai.d.cts", "default": "./embeddings/cloudflare_workersai.d.ts"}, "import": "./embeddings/cloudflare_workersai.js", "require": "./embeddings/cloudflare_workersai.cjs"}, "./embeddings/cohere": {"types": {"import": "./embeddings/cohere.d.ts", "require": "./embeddings/cohere.d.cts", "default": "./embeddings/cohere.d.ts"}, "import": "./embeddings/cohere.js", "require": "./embeddings/cohere.cjs"}, "./embeddings/fireworks": {"types": {"import": "./embeddings/fireworks.d.ts", "require": "./embeddings/fireworks.d.cts", "default": "./embeddings/fireworks.d.ts"}, "import": "./embeddings/fireworks.js", "require": "./embeddings/fireworks.cjs"}, "./embeddings/googlepalm": {"types": {"import": "./embeddings/googlepalm.d.ts", "require": "./embeddings/googlepalm.d.cts", "default": "./embeddings/googlepalm.d.ts"}, "import": "./embeddings/googlepalm.js", "require": "./embeddings/googlepalm.cjs"}, "./embeddings/googlevertexai": {"types": {"import": "./embeddings/googlevertexai.d.ts", "require": "./embeddings/googlevertexai.d.cts", "default": "./embeddings/googlevertexai.d.ts"}, "import": "./embeddings/googlevertexai.js", "require": "./embeddings/googlevertexai.cjs"}, "./embeddings/gradient_ai": {"types": {"import": "./embeddings/gradient_ai.d.ts", "require": "./embeddings/gradient_ai.d.cts", "default": "./embeddings/gradient_ai.d.ts"}, "import": "./embeddings/gradient_ai.js", "require": "./embeddings/gradient_ai.cjs"}, "./embeddings/hf": {"types": {"import": "./embeddings/hf.d.ts", "require": "./embeddings/hf.d.cts", "default": "./embeddings/hf.d.ts"}, "import": "./embeddings/hf.js", "require": "./embeddings/hf.cjs"}, "./embeddings/hf_transformers": {"types": {"import": "./embeddings/hf_transformers.d.ts", "require": "./embeddings/hf_transformers.d.cts", "default": "./embeddings/hf_transformers.d.ts"}, "import": "./embeddings/hf_transformers.js", "require": "./embeddings/hf_transformers.cjs"}, "./embeddings/llama_cpp": {"types": {"import": "./embeddings/llama_cpp.d.ts", "require": "./embeddings/llama_cpp.d.cts", "default": "./embeddings/llama_cpp.d.ts"}, "import": "./embeddings/llama_cpp.js", "require": "./embeddings/llama_cpp.cjs"}, "./embeddings/minimax": {"types": {"import": "./embeddings/minimax.d.ts", "require": "./embeddings/minimax.d.cts", "default": "./embeddings/minimax.d.ts"}, "import": "./embeddings/minimax.js", "require": "./embeddings/minimax.cjs"}, "./embeddings/ollama": {"types": {"import": "./embeddings/ollama.d.ts", "require": "./embeddings/ollama.d.cts", "default": "./embeddings/ollama.d.ts"}, "import": "./embeddings/ollama.js", "require": "./embeddings/ollama.cjs"}, "./embeddings/premai": {"types": {"import": "./embeddings/premai.d.ts", "require": "./embeddings/premai.d.cts", "default": "./embeddings/premai.d.ts"}, "import": "./embeddings/premai.js", "require": "./embeddings/premai.cjs"}, "./embeddings/tensorflow": {"types": {"import": "./embeddings/tensorflow.d.ts", "require": "./embeddings/tensorflow.d.cts", "default": "./embeddings/tensorflow.d.ts"}, "import": "./embeddings/tensorflow.js", "require": "./embeddings/tensorflow.cjs"}, "./embeddings/togetherai": {"types": {"import": "./embeddings/togetherai.d.ts", "require": "./embeddings/togetherai.d.cts", "default": "./embeddings/togetherai.d.ts"}, "import": "./embeddings/togetherai.js", "require": "./embeddings/togetherai.cjs"}, "./embeddings/voyage": {"types": {"import": "./embeddings/voyage.d.ts", "require": "./embeddings/voyage.d.cts", "default": "./embeddings/voyage.d.ts"}, "import": "./embeddings/voyage.js", "require": "./embeddings/voyage.cjs"}, "./embeddings/zhipuai": {"types": {"import": "./embeddings/zhipuai.d.ts", "require": "./embeddings/zhipuai.d.cts", "default": "./embeddings/zhipuai.d.ts"}, "import": "./embeddings/zhipuai.js", "require": "./embeddings/zhipuai.cjs"}, "./llms/ai21": {"types": {"import": "./llms/ai21.d.ts", "require": "./llms/ai21.d.cts", "default": "./llms/ai21.d.ts"}, "import": "./llms/ai21.js", "require": "./llms/ai21.cjs"}, "./llms/aleph_alpha": {"types": {"import": "./llms/aleph_alpha.d.ts", "require": "./llms/aleph_alpha.d.cts", "default": "./llms/aleph_alpha.d.ts"}, "import": "./llms/aleph_alpha.js", "require": "./llms/aleph_alpha.cjs"}, "./llms/bedrock": {"types": {"import": "./llms/bedrock.d.ts", "require": "./llms/bedrock.d.cts", "default": "./llms/bedrock.d.ts"}, "import": "./llms/bedrock.js", "require": "./llms/bedrock.cjs"}, "./llms/bedrock/web": {"types": {"import": "./llms/bedrock/web.d.ts", "require": "./llms/bedrock/web.d.cts", "default": "./llms/bedrock/web.d.ts"}, "import": "./llms/bedrock/web.js", "require": "./llms/bedrock/web.cjs"}, "./llms/cloudflare_workersai": {"types": {"import": "./llms/cloudflare_workersai.d.ts", "require": "./llms/cloudflare_workersai.d.cts", "default": "./llms/cloudflare_workersai.d.ts"}, "import": "./llms/cloudflare_workersai.js", "require": "./llms/cloudflare_workersai.cjs"}, "./llms/cohere": {"types": {"import": "./llms/cohere.d.ts", "require": "./llms/cohere.d.cts", "default": "./llms/cohere.d.ts"}, "import": "./llms/cohere.js", "require": "./llms/cohere.cjs"}, "./llms/fireworks": {"types": {"import": "./llms/fireworks.d.ts", "require": "./llms/fireworks.d.cts", "default": "./llms/fireworks.d.ts"}, "import": "./llms/fireworks.js", "require": "./llms/fireworks.cjs"}, "./llms/googlepalm": {"types": {"import": "./llms/googlepalm.d.ts", "require": "./llms/googlepalm.d.cts", "default": "./llms/googlepalm.d.ts"}, "import": "./llms/googlepalm.js", "require": "./llms/googlepalm.cjs"}, "./llms/googlevertexai": {"types": {"import": "./llms/googlevertexai.d.ts", "require": "./llms/googlevertexai.d.cts", "default": "./llms/googlevertexai.d.ts"}, "import": "./llms/googlevertexai.js", "require": "./llms/googlevertexai.cjs"}, "./llms/googlevertexai/web": {"types": {"import": "./llms/googlevertexai/web.d.ts", "require": "./llms/googlevertexai/web.d.cts", "default": "./llms/googlevertexai/web.d.ts"}, "import": "./llms/googlevertexai/web.js", "require": "./llms/googlevertexai/web.cjs"}, "./llms/gradient_ai": {"types": {"import": "./llms/gradient_ai.d.ts", "require": "./llms/gradient_ai.d.cts", "default": "./llms/gradient_ai.d.ts"}, "import": "./llms/gradient_ai.js", "require": "./llms/gradient_ai.cjs"}, "./llms/hf": {"types": {"import": "./llms/hf.d.ts", "require": "./llms/hf.d.cts", "default": "./llms/hf.d.ts"}, "import": "./llms/hf.js", "require": "./llms/hf.cjs"}, "./llms/llama_cpp": {"types": {"import": "./llms/llama_cpp.d.ts", "require": "./llms/llama_cpp.d.cts", "default": "./llms/llama_cpp.d.ts"}, "import": "./llms/llama_cpp.js", "require": "./llms/llama_cpp.cjs"}, "./llms/ollama": {"types": {"import": "./llms/ollama.d.ts", "require": "./llms/ollama.d.cts", "default": "./llms/ollama.d.ts"}, "import": "./llms/ollama.js", "require": "./llms/ollama.cjs"}, "./llms/portkey": {"types": {"import": "./llms/portkey.d.ts", "require": "./llms/portkey.d.cts", "default": "./llms/portkey.d.ts"}, "import": "./llms/portkey.js", "require": "./llms/portkey.cjs"}, "./llms/raycast": {"types": {"import": "./llms/raycast.d.ts", "require": "./llms/raycast.d.cts", "default": "./llms/raycast.d.ts"}, "import": "./llms/raycast.js", "require": "./llms/raycast.cjs"}, "./llms/replicate": {"types": {"import": "./llms/replicate.d.ts", "require": "./llms/replicate.d.cts", "default": "./llms/replicate.d.ts"}, "import": "./llms/replicate.js", "require": "./llms/replicate.cjs"}, "./llms/sagemaker_endpoint": {"types": {"import": "./llms/sagemaker_endpoint.d.ts", "require": "./llms/sagemaker_endpoint.d.cts", "default": "./llms/sagemaker_endpoint.d.ts"}, "import": "./llms/sagemaker_endpoint.js", "require": "./llms/sagemaker_endpoint.cjs"}, "./llms/togetherai": {"types": {"import": "./llms/togetherai.d.ts", "require": "./llms/togetherai.d.cts", "default": "./llms/togetherai.d.ts"}, "import": "./llms/togetherai.js", "require": "./llms/togetherai.cjs"}, "./llms/watsonx_ai": {"types": {"import": "./llms/watsonx_ai.d.ts", "require": "./llms/watsonx_ai.d.cts", "default": "./llms/watsonx_ai.d.ts"}, "import": "./llms/watsonx_ai.js", "require": "./llms/watsonx_ai.cjs"}, "./llms/writer": {"types": {"import": "./llms/writer.d.ts", "require": "./llms/writer.d.cts", "default": "./llms/writer.d.ts"}, "import": "./llms/writer.js", "require": "./llms/writer.cjs"}, "./llms/yandex": {"types": {"import": "./llms/yandex.d.ts", "require": "./llms/yandex.d.cts", "default": "./llms/yandex.d.ts"}, "import": "./llms/yandex.js", "require": "./llms/yandex.cjs"}, "./vectorstores/analyticdb": {"types": {"import": "./vectorstores/analyticdb.d.ts", "require": "./vectorstores/analyticdb.d.cts", "default": "./vectorstores/analyticdb.d.ts"}, "import": "./vectorstores/analyticdb.js", "require": "./vectorstores/analyticdb.cjs"}, "./vectorstores/astradb": {"types": {"import": "./vectorstores/astradb.d.ts", "require": "./vectorstores/astradb.d.cts", "default": "./vectorstores/astradb.d.ts"}, "import": "./vectorstores/astradb.js", "require": "./vectorstores/astradb.cjs"}, "./vectorstores/azure_aisearch": {"types": {"import": "./vectorstores/azure_aisearch.d.ts", "require": "./vectorstores/azure_aisearch.d.cts", "default": "./vectorstores/azure_aisearch.d.ts"}, "import": "./vectorstores/azure_aisearch.js", "require": "./vectorstores/azure_aisearch.cjs"}, "./vectorstores/azure_cosmosdb": {"types": {"import": "./vectorstores/azure_cosmosdb.d.ts", "require": "./vectorstores/azure_cosmosdb.d.cts", "default": "./vectorstores/azure_cosmosdb.d.ts"}, "import": "./vectorstores/azure_cosmosdb.js", "require": "./vectorstores/azure_cosmosdb.cjs"}, "./vectorstores/cassandra": {"types": {"import": "./vectorstores/cassandra.d.ts", "require": "./vectorstores/cassandra.d.cts", "default": "./vectorstores/cassandra.d.ts"}, "import": "./vectorstores/cassandra.js", "require": "./vectorstores/cassandra.cjs"}, "./vectorstores/chroma": {"types": {"import": "./vectorstores/chroma.d.ts", "require": "./vectorstores/chroma.d.cts", "default": "./vectorstores/chroma.d.ts"}, "import": "./vectorstores/chroma.js", "require": "./vectorstores/chroma.cjs"}, "./vectorstores/clickhouse": {"types": {"import": "./vectorstores/clickhouse.d.ts", "require": "./vectorstores/clickhouse.d.cts", "default": "./vectorstores/clickhouse.d.ts"}, "import": "./vectorstores/clickhouse.js", "require": "./vectorstores/clickhouse.cjs"}, "./vectorstores/closevector/node": {"types": {"import": "./vectorstores/closevector/node.d.ts", "require": "./vectorstores/closevector/node.d.cts", "default": "./vectorstores/closevector/node.d.ts"}, "import": "./vectorstores/closevector/node.js", "require": "./vectorstores/closevector/node.cjs"}, "./vectorstores/closevector/web": {"types": {"import": "./vectorstores/closevector/web.d.ts", "require": "./vectorstores/closevector/web.d.cts", "default": "./vectorstores/closevector/web.d.ts"}, "import": "./vectorstores/closevector/web.js", "require": "./vectorstores/closevector/web.cjs"}, "./vectorstores/cloudflare_vectorize": {"types": {"import": "./vectorstores/cloudflare_vectorize.d.ts", "require": "./vectorstores/cloudflare_vectorize.d.cts", "default": "./vectorstores/cloudflare_vectorize.d.ts"}, "import": "./vectorstores/cloudflare_vectorize.js", "require": "./vectorstores/cloudflare_vectorize.cjs"}, "./vectorstores/convex": {"types": {"import": "./vectorstores/convex.d.ts", "require": "./vectorstores/convex.d.cts", "default": "./vectorstores/convex.d.ts"}, "import": "./vectorstores/convex.js", "require": "./vectorstores/convex.cjs"}, "./vectorstores/couchbase": {"types": {"import": "./vectorstores/couchbase.d.ts", "require": "./vectorstores/couchbase.d.cts", "default": "./vectorstores/couchbase.d.ts"}, "import": "./vectorstores/couchbase.js", "require": "./vectorstores/couchbase.cjs"}, "./vectorstores/elasticsearch": {"types": {"import": "./vectorstores/elasticsearch.d.ts", "require": "./vectorstores/elasticsearch.d.cts", "default": "./vectorstores/elasticsearch.d.ts"}, "import": "./vectorstores/elasticsearch.js", "require": "./vectorstores/elasticsearch.cjs"}, "./vectorstores/faiss": {"types": {"import": "./vectorstores/faiss.d.ts", "require": "./vectorstores/faiss.d.cts", "default": "./vectorstores/faiss.d.ts"}, "import": "./vectorstores/faiss.js", "require": "./vectorstores/faiss.cjs"}, "./vectorstores/googlevertexai": {"types": {"import": "./vectorstores/googlevertexai.d.ts", "require": "./vectorstores/googlevertexai.d.cts", "default": "./vectorstores/googlevertexai.d.ts"}, "import": "./vectorstores/googlevertexai.js", "require": "./vectorstores/googlevertexai.cjs"}, "./vectorstores/hnswlib": {"types": {"import": "./vectorstores/hnswlib.d.ts", "require": "./vectorstores/hnswlib.d.cts", "default": "./vectorstores/hnswlib.d.ts"}, "import": "./vectorstores/hnswlib.js", "require": "./vectorstores/hnswlib.cjs"}, "./vectorstores/lancedb": {"types": {"import": "./vectorstores/lancedb.d.ts", "require": "./vectorstores/lancedb.d.cts", "default": "./vectorstores/lancedb.d.ts"}, "import": "./vectorstores/lancedb.js", "require": "./vectorstores/lancedb.cjs"}, "./vectorstores/milvus": {"types": {"import": "./vectorstores/milvus.d.ts", "require": "./vectorstores/milvus.d.cts", "default": "./vectorstores/milvus.d.ts"}, "import": "./vectorstores/milvus.js", "require": "./vectorstores/milvus.cjs"}, "./vectorstores/momento_vector_index": {"types": {"import": "./vectorstores/momento_vector_index.d.ts", "require": "./vectorstores/momento_vector_index.d.cts", "default": "./vectorstores/momento_vector_index.d.ts"}, "import": "./vectorstores/momento_vector_index.js", "require": "./vectorstores/momento_vector_index.cjs"}, "./vectorstores/mongodb_atlas": {"types": {"import": "./vectorstores/mongodb_atlas.d.ts", "require": "./vectorstores/mongodb_atlas.d.cts", "default": "./vectorstores/mongodb_atlas.d.ts"}, "import": "./vectorstores/mongodb_atlas.js", "require": "./vectorstores/mongodb_atlas.cjs"}, "./vectorstores/myscale": {"types": {"import": "./vectorstores/myscale.d.ts", "require": "./vectorstores/myscale.d.cts", "default": "./vectorstores/myscale.d.ts"}, "import": "./vectorstores/myscale.js", "require": "./vectorstores/myscale.cjs"}, "./vectorstores/neo4j_vector": {"types": {"import": "./vectorstores/neo4j_vector.d.ts", "require": "./vectorstores/neo4j_vector.d.cts", "default": "./vectorstores/neo4j_vector.d.ts"}, "import": "./vectorstores/neo4j_vector.js", "require": "./vectorstores/neo4j_vector.cjs"}, "./vectorstores/opensearch": {"types": {"import": "./vectorstores/opensearch.d.ts", "require": "./vectorstores/opensearch.d.cts", "default": "./vectorstores/opensearch.d.ts"}, "import": "./vectorstores/opensearch.js", "require": "./vectorstores/opensearch.cjs"}, "./vectorstores/pgvector": {"types": {"import": "./vectorstores/pgvector.d.ts", "require": "./vectorstores/pgvector.d.cts", "default": "./vectorstores/pgvector.d.ts"}, "import": "./vectorstores/pgvector.js", "require": "./vectorstores/pgvector.cjs"}, "./vectorstores/pinecone": {"types": {"import": "./vectorstores/pinecone.d.ts", "require": "./vectorstores/pinecone.d.cts", "default": "./vectorstores/pinecone.d.ts"}, "import": "./vectorstores/pinecone.js", "require": "./vectorstores/pinecone.cjs"}, "./vectorstores/prisma": {"types": {"import": "./vectorstores/prisma.d.ts", "require": "./vectorstores/prisma.d.cts", "default": "./vectorstores/prisma.d.ts"}, "import": "./vectorstores/prisma.js", "require": "./vectorstores/prisma.cjs"}, "./vectorstores/qdrant": {"types": {"import": "./vectorstores/qdrant.d.ts", "require": "./vectorstores/qdrant.d.cts", "default": "./vectorstores/qdrant.d.ts"}, "import": "./vectorstores/qdrant.js", "require": "./vectorstores/qdrant.cjs"}, "./vectorstores/redis": {"types": {"import": "./vectorstores/redis.d.ts", "require": "./vectorstores/redis.d.cts", "default": "./vectorstores/redis.d.ts"}, "import": "./vectorstores/redis.js", "require": "./vectorstores/redis.cjs"}, "./vectorstores/rockset": {"types": {"import": "./vectorstores/rockset.d.ts", "require": "./vectorstores/rockset.d.cts", "default": "./vectorstores/rockset.d.ts"}, "import": "./vectorstores/rockset.js", "require": "./vectorstores/rockset.cjs"}, "./vectorstores/singlestore": {"types": {"import": "./vectorstores/singlestore.d.ts", "require": "./vectorstores/singlestore.d.cts", "default": "./vectorstores/singlestore.d.ts"}, "import": "./vectorstores/singlestore.js", "require": "./vectorstores/singlestore.cjs"}, "./vectorstores/supabase": {"types": {"import": "./vectorstores/supabase.d.ts", "require": "./vectorstores/supabase.d.cts", "default": "./vectorstores/supabase.d.ts"}, "import": "./vectorstores/supabase.js", "require": "./vectorstores/supabase.cjs"}, "./vectorstores/tigris": {"types": {"import": "./vectorstores/tigris.d.ts", "require": "./vectorstores/tigris.d.cts", "default": "./vectorstores/tigris.d.ts"}, "import": "./vectorstores/tigris.js", "require": "./vectorstores/tigris.cjs"}, "./vectorstores/turbopuffer": {"types": {"import": "./vectorstores/turbopuffer.d.ts", "require": "./vectorstores/turbopuffer.d.cts", "default": "./vectorstores/turbopuffer.d.ts"}, "import": "./vectorstores/turbopuffer.js", "require": "./vectorstores/turbopuffer.cjs"}, "./vectorstores/typeorm": {"types": {"import": "./vectorstores/typeorm.d.ts", "require": "./vectorstores/typeorm.d.cts", "default": "./vectorstores/typeorm.d.ts"}, "import": "./vectorstores/typeorm.js", "require": "./vectorstores/typeorm.cjs"}, "./vectorstores/typesense": {"types": {"import": "./vectorstores/typesense.d.ts", "require": "./vectorstores/typesense.d.cts", "default": "./vectorstores/typesense.d.ts"}, "import": "./vectorstores/typesense.js", "require": "./vectorstores/typesense.cjs"}, "./vectorstores/upstash": {"types": {"import": "./vectorstores/upstash.d.ts", "require": "./vectorstores/upstash.d.cts", "default": "./vectorstores/upstash.d.ts"}, "import": "./vectorstores/upstash.js", "require": "./vectorstores/upstash.cjs"}, "./vectorstores/usearch": {"types": {"import": "./vectorstores/usearch.d.ts", "require": "./vectorstores/usearch.d.cts", "default": "./vectorstores/usearch.d.ts"}, "import": "./vectorstores/usearch.js", "require": "./vectorstores/usearch.cjs"}, "./vectorstores/vectara": {"types": {"import": "./vectorstores/vectara.d.ts", "require": "./vectorstores/vectara.d.cts", "default": "./vectorstores/vectara.d.ts"}, "import": "./vectorstores/vectara.js", "require": "./vectorstores/vectara.cjs"}, "./vectorstores/vercel_postgres": {"types": {"import": "./vectorstores/vercel_postgres.d.ts", "require": "./vectorstores/vercel_postgres.d.cts", "default": "./vectorstores/vercel_postgres.d.ts"}, "import": "./vectorstores/vercel_postgres.js", "require": "./vectorstores/vercel_postgres.cjs"}, "./vectorstores/voy": {"types": {"import": "./vectorstores/voy.d.ts", "require": "./vectorstores/voy.d.cts", "default": "./vectorstores/voy.d.ts"}, "import": "./vectorstores/voy.js", "require": "./vectorstores/voy.cjs"}, "./vectorstores/weaviate": {"types": {"import": "./vectorstores/weaviate.d.ts", "require": "./vectorstores/weaviate.d.cts", "default": "./vectorstores/weaviate.d.ts"}, "import": "./vectorstores/weaviate.js", "require": "./vectorstores/weaviate.cjs"}, "./vectorstores/xata": {"types": {"import": "./vectorstores/xata.d.ts", "require": "./vectorstores/xata.d.cts", "default": "./vectorstores/xata.d.ts"}, "import": "./vectorstores/xata.js", "require": "./vectorstores/xata.cjs"}, "./vectorstores/zep": {"types": {"import": "./vectorstores/zep.d.ts", "require": "./vectorstores/zep.d.cts", "default": "./vectorstores/zep.d.ts"}, "import": "./vectorstores/zep.js", "require": "./vectorstores/zep.cjs"}, "./chat_models/alibaba_tongyi": {"types": {"import": "./chat_models/alibaba_tongyi.d.ts", "require": "./chat_models/alibaba_tongyi.d.cts", "default": "./chat_models/alibaba_tongyi.d.ts"}, "import": "./chat_models/alibaba_tongyi.js", "require": "./chat_models/alibaba_tongyi.cjs"}, "./chat_models/baiduwenxin": {"types": {"import": "./chat_models/baiduwenxin.d.ts", "require": "./chat_models/baiduwenxin.d.cts", "default": "./chat_models/baiduwenxin.d.ts"}, "import": "./chat_models/baiduwenxin.js", "require": "./chat_models/baiduwenxin.cjs"}, "./chat_models/bedrock": {"types": {"import": "./chat_models/bedrock.d.ts", "require": "./chat_models/bedrock.d.cts", "default": "./chat_models/bedrock.d.ts"}, "import": "./chat_models/bedrock.js", "require": "./chat_models/bedrock.cjs"}, "./chat_models/bedrock/web": {"types": {"import": "./chat_models/bedrock/web.d.ts", "require": "./chat_models/bedrock/web.d.cts", "default": "./chat_models/bedrock/web.d.ts"}, "import": "./chat_models/bedrock/web.js", "require": "./chat_models/bedrock/web.cjs"}, "./chat_models/cloudflare_workersai": {"types": {"import": "./chat_models/cloudflare_workersai.d.ts", "require": "./chat_models/cloudflare_workersai.d.cts", "default": "./chat_models/cloudflare_workersai.d.ts"}, "import": "./chat_models/cloudflare_workersai.js", "require": "./chat_models/cloudflare_workersai.cjs"}, "./chat_models/fireworks": {"types": {"import": "./chat_models/fireworks.d.ts", "require": "./chat_models/fireworks.d.cts", "default": "./chat_models/fireworks.d.ts"}, "import": "./chat_models/fireworks.js", "require": "./chat_models/fireworks.cjs"}, "./chat_models/googlevertexai": {"types": {"import": "./chat_models/googlevertexai.d.ts", "require": "./chat_models/googlevertexai.d.cts", "default": "./chat_models/googlevertexai.d.ts"}, "import": "./chat_models/googlevertexai.js", "require": "./chat_models/googlevertexai.cjs"}, "./chat_models/googlevertexai/web": {"types": {"import": "./chat_models/googlevertexai/web.d.ts", "require": "./chat_models/googlevertexai/web.d.cts", "default": "./chat_models/googlevertexai/web.d.ts"}, "import": "./chat_models/googlevertexai/web.js", "require": "./chat_models/googlevertexai/web.cjs"}, "./chat_models/googlepalm": {"types": {"import": "./chat_models/googlepalm.d.ts", "require": "./chat_models/googlepalm.d.cts", "default": "./chat_models/googlepalm.d.ts"}, "import": "./chat_models/googlepalm.js", "require": "./chat_models/googlepalm.cjs"}, "./chat_models/iflytek_xinghuo": {"types": {"import": "./chat_models/iflytek_xinghuo.d.ts", "require": "./chat_models/iflytek_xinghuo.d.cts", "default": "./chat_models/iflytek_xinghuo.d.ts"}, "import": "./chat_models/iflytek_xinghuo.js", "require": "./chat_models/iflytek_xinghuo.cjs"}, "./chat_models/iflytek_xinghuo/web": {"types": {"import": "./chat_models/iflytek_xinghuo/web.d.ts", "require": "./chat_models/iflytek_xinghuo/web.d.cts", "default": "./chat_models/iflytek_xinghuo/web.d.ts"}, "import": "./chat_models/iflytek_xinghuo/web.js", "require": "./chat_models/iflytek_xinghuo/web.cjs"}, "./chat_models/llama_cpp": {"types": {"import": "./chat_models/llama_cpp.d.ts", "require": "./chat_models/llama_cpp.d.cts", "default": "./chat_models/llama_cpp.d.ts"}, "import": "./chat_models/llama_cpp.js", "require": "./chat_models/llama_cpp.cjs"}, "./chat_models/minimax": {"types": {"import": "./chat_models/minimax.d.ts", "require": "./chat_models/minimax.d.cts", "default": "./chat_models/minimax.d.ts"}, "import": "./chat_models/minimax.js", "require": "./chat_models/minimax.cjs"}, "./chat_models/ollama": {"types": {"import": "./chat_models/ollama.d.ts", "require": "./chat_models/ollama.d.cts", "default": "./chat_models/ollama.d.ts"}, "import": "./chat_models/ollama.js", "require": "./chat_models/ollama.cjs"}, "./chat_models/portkey": {"types": {"import": "./chat_models/portkey.d.ts", "require": "./chat_models/portkey.d.cts", "default": "./chat_models/portkey.d.ts"}, "import": "./chat_models/portkey.js", "require": "./chat_models/portkey.cjs"}, "./chat_models/premai": {"types": {"import": "./chat_models/premai.d.ts", "require": "./chat_models/premai.d.cts", "default": "./chat_models/premai.d.ts"}, "import": "./chat_models/premai.js", "require": "./chat_models/premai.cjs"}, "./chat_models/togetherai": {"types": {"import": "./chat_models/togetherai.d.ts", "require": "./chat_models/togetherai.d.cts", "default": "./chat_models/togetherai.d.ts"}, "import": "./chat_models/togetherai.js", "require": "./chat_models/togetherai.cjs"}, "./chat_models/yandex": {"types": {"import": "./chat_models/yandex.d.ts", "require": "./chat_models/yandex.d.cts", "default": "./chat_models/yandex.d.ts"}, "import": "./chat_models/yandex.js", "require": "./chat_models/yandex.cjs"}, "./chat_models/zhipuai": {"types": {"import": "./chat_models/zhipuai.d.ts", "require": "./chat_models/zhipuai.d.cts", "default": "./chat_models/zhipuai.d.ts"}, "import": "./chat_models/zhipuai.js", "require": "./chat_models/zhipuai.cjs"}, "./callbacks/handlers/llmonitor": {"types": {"import": "./callbacks/handlers/llmonitor.d.ts", "require": "./callbacks/handlers/llmonitor.d.cts", "default": "./callbacks/handlers/llmonitor.d.ts"}, "import": "./callbacks/handlers/llmonitor.js", "require": "./callbacks/handlers/llmonitor.cjs"}, "./callbacks/handlers/lunary": {"types": {"import": "./callbacks/handlers/lunary.d.ts", "require": "./callbacks/handlers/lunary.d.cts", "default": "./callbacks/handlers/lunary.d.ts"}, "import": "./callbacks/handlers/lunary.js", "require": "./callbacks/handlers/lunary.cjs"}, "./retrievers/amazon_kendra": {"types": {"import": "./retrievers/amazon_kendra.d.ts", "require": "./retrievers/amazon_kendra.d.cts", "default": "./retrievers/amazon_kendra.d.ts"}, "import": "./retrievers/amazon_kendra.js", "require": "./retrievers/amazon_kendra.cjs"}, "./retrievers/amazon_knowledge_base": {"types": {"import": "./retrievers/amazon_knowledge_base.d.ts", "require": "./retrievers/amazon_knowledge_base.d.cts", "default": "./retrievers/amazon_knowledge_base.d.ts"}, "import": "./retrievers/amazon_knowledge_base.js", "require": "./retrievers/amazon_knowledge_base.cjs"}, "./retrievers/chaindesk": {"types": {"import": "./retrievers/chaindesk.d.ts", "require": "./retrievers/chaindesk.d.cts", "default": "./retrievers/chaindesk.d.ts"}, "import": "./retrievers/chaindesk.js", "require": "./retrievers/chaindesk.cjs"}, "./retrievers/databerry": {"types": {"import": "./retrievers/databerry.d.ts", "require": "./retrievers/databerry.d.cts", "default": "./retrievers/databerry.d.ts"}, "import": "./retrievers/databerry.js", "require": "./retrievers/databerry.cjs"}, "./retrievers/dria": {"types": {"import": "./retrievers/dria.d.ts", "require": "./retrievers/dria.d.cts", "default": "./retrievers/dria.d.ts"}, "import": "./retrievers/dria.js", "require": "./retrievers/dria.cjs"}, "./retrievers/metal": {"types": {"import": "./retrievers/metal.d.ts", "require": "./retrievers/metal.d.cts", "default": "./retrievers/metal.d.ts"}, "import": "./retrievers/metal.js", "require": "./retrievers/metal.cjs"}, "./retrievers/remote": {"types": {"import": "./retrievers/remote.d.ts", "require": "./retrievers/remote.d.cts", "default": "./retrievers/remote.d.ts"}, "import": "./retrievers/remote.js", "require": "./retrievers/remote.cjs"}, "./retrievers/supabase": {"types": {"import": "./retrievers/supabase.d.ts", "require": "./retrievers/supabase.d.cts", "default": "./retrievers/supabase.d.ts"}, "import": "./retrievers/supabase.js", "require": "./retrievers/supabase.cjs"}, "./retrievers/tavily_search_api": {"types": {"import": "./retrievers/tavily_search_api.d.ts", "require": "./retrievers/tavily_search_api.d.cts", "default": "./retrievers/tavily_search_api.d.ts"}, "import": "./retrievers/tavily_search_api.js", "require": "./retrievers/tavily_search_api.cjs"}, "./retrievers/vectara_summary": {"types": {"import": "./retrievers/vectara_summary.d.ts", "require": "./retrievers/vectara_summary.d.cts", "default": "./retrievers/vectara_summary.d.ts"}, "import": "./retrievers/vectara_summary.js", "require": "./retrievers/vectara_summary.cjs"}, "./retrievers/vespa": {"types": {"import": "./retrievers/vespa.d.ts", "require": "./retrievers/vespa.d.cts", "default": "./retrievers/vespa.d.ts"}, "import": "./retrievers/vespa.js", "require": "./retrievers/vespa.cjs"}, "./retrievers/zep": {"types": {"import": "./retrievers/zep.d.ts", "require": "./retrievers/zep.d.cts", "default": "./retrievers/zep.d.ts"}, "import": "./retrievers/zep.js", "require": "./retrievers/zep.cjs"}, "./caches/cloudflare_kv": {"types": {"import": "./caches/cloudflare_kv.d.ts", "require": "./caches/cloudflare_kv.d.cts", "default": "./caches/cloudflare_kv.d.ts"}, "import": "./caches/cloudflare_kv.js", "require": "./caches/cloudflare_kv.cjs"}, "./caches/ioredis": {"types": {"import": "./caches/ioredis.d.ts", "require": "./caches/ioredis.d.cts", "default": "./caches/ioredis.d.ts"}, "import": "./caches/ioredis.js", "require": "./caches/ioredis.cjs"}, "./caches/momento": {"types": {"import": "./caches/momento.d.ts", "require": "./caches/momento.d.cts", "default": "./caches/momento.d.ts"}, "import": "./caches/momento.js", "require": "./caches/momento.cjs"}, "./caches/upstash_redis": {"types": {"import": "./caches/upstash_redis.d.ts", "require": "./caches/upstash_redis.d.cts", "default": "./caches/upstash_redis.d.ts"}, "import": "./caches/upstash_redis.js", "require": "./caches/upstash_redis.cjs"}, "./graphs/neo4j_graph": {"types": {"import": "./graphs/neo4j_graph.d.ts", "require": "./graphs/neo4j_graph.d.cts", "default": "./graphs/neo4j_graph.d.ts"}, "import": "./graphs/neo4j_graph.js", "require": "./graphs/neo4j_graph.cjs"}, "./graphs/memgraph_graph": {"types": {"import": "./graphs/memgraph_graph.d.ts", "require": "./graphs/memgraph_graph.d.cts", "default": "./graphs/memgraph_graph.d.ts"}, "import": "./graphs/memgraph_graph.js", "require": "./graphs/memgraph_graph.cjs"}, "./document_transformers/html_to_text": {"types": {"import": "./document_transformers/html_to_text.d.ts", "require": "./document_transformers/html_to_text.d.cts", "default": "./document_transformers/html_to_text.d.ts"}, "import": "./document_transformers/html_to_text.js", "require": "./document_transformers/html_to_text.cjs"}, "./document_transformers/mozilla_readability": {"types": {"import": "./document_transformers/mozilla_readability.d.ts", "require": "./document_transformers/mozilla_readability.d.cts", "default": "./document_transformers/mozilla_readability.d.ts"}, "import": "./document_transformers/mozilla_readability.js", "require": "./document_transformers/mozilla_readability.cjs"}, "./storage/cassandra": {"types": {"import": "./storage/cassandra.d.ts", "require": "./storage/cassandra.d.cts", "default": "./storage/cassandra.d.ts"}, "import": "./storage/cassandra.js", "require": "./storage/cassandra.cjs"}, "./storage/convex": {"types": {"import": "./storage/convex.d.ts", "require": "./storage/convex.d.cts", "default": "./storage/convex.d.ts"}, "import": "./storage/convex.js", "require": "./storage/convex.cjs"}, "./storage/ioredis": {"types": {"import": "./storage/ioredis.d.ts", "require": "./storage/ioredis.d.cts", "default": "./storage/ioredis.d.ts"}, "import": "./storage/ioredis.js", "require": "./storage/ioredis.cjs"}, "./storage/upstash_redis": {"types": {"import": "./storage/upstash_redis.d.ts", "require": "./storage/upstash_redis.d.cts", "default": "./storage/upstash_redis.d.ts"}, "import": "./storage/upstash_redis.js", "require": "./storage/upstash_redis.cjs"}, "./storage/vercel_kv": {"types": {"import": "./storage/vercel_kv.d.ts", "require": "./storage/vercel_kv.d.cts", "default": "./storage/vercel_kv.d.ts"}, "import": "./storage/vercel_kv.js", "require": "./storage/vercel_kv.cjs"}, "./stores/doc/base": {"types": {"import": "./stores/doc/base.d.ts", "require": "./stores/doc/base.d.cts", "default": "./stores/doc/base.d.ts"}, "import": "./stores/doc/base.js", "require": "./stores/doc/base.cjs"}, "./stores/doc/in_memory": {"types": {"import": "./stores/doc/in_memory.d.ts", "require": "./stores/doc/in_memory.d.cts", "default": "./stores/doc/in_memory.d.ts"}, "import": "./stores/doc/in_memory.js", "require": "./stores/doc/in_memory.cjs"}, "./stores/message/astradb": {"types": {"import": "./stores/message/astradb.d.ts", "require": "./stores/message/astradb.d.cts", "default": "./stores/message/astradb.d.ts"}, "import": "./stores/message/astradb.js", "require": "./stores/message/astradb.cjs"}, "./stores/message/cassandra": {"types": {"import": "./stores/message/cassandra.d.ts", "require": "./stores/message/cassandra.d.cts", "default": "./stores/message/cassandra.d.ts"}, "import": "./stores/message/cassandra.js", "require": "./stores/message/cassandra.cjs"}, "./stores/message/cloudflare_d1": {"types": {"import": "./stores/message/cloudflare_d1.d.ts", "require": "./stores/message/cloudflare_d1.d.cts", "default": "./stores/message/cloudflare_d1.d.ts"}, "import": "./stores/message/cloudflare_d1.js", "require": "./stores/message/cloudflare_d1.cjs"}, "./stores/message/convex": {"types": {"import": "./stores/message/convex.d.ts", "require": "./stores/message/convex.d.cts", "default": "./stores/message/convex.d.ts"}, "import": "./stores/message/convex.js", "require": "./stores/message/convex.cjs"}, "./stores/message/dynamodb": {"types": {"import": "./stores/message/dynamodb.d.ts", "require": "./stores/message/dynamodb.d.cts", "default": "./stores/message/dynamodb.d.ts"}, "import": "./stores/message/dynamodb.js", "require": "./stores/message/dynamodb.cjs"}, "./stores/message/firestore": {"types": {"import": "./stores/message/firestore.d.ts", "require": "./stores/message/firestore.d.cts", "default": "./stores/message/firestore.d.ts"}, "import": "./stores/message/firestore.js", "require": "./stores/message/firestore.cjs"}, "./stores/message/in_memory": {"types": {"import": "./stores/message/in_memory.d.ts", "require": "./stores/message/in_memory.d.cts", "default": "./stores/message/in_memory.d.ts"}, "import": "./stores/message/in_memory.js", "require": "./stores/message/in_memory.cjs"}, "./stores/message/ipfs_datastore": {"types": {"import": "./stores/message/ipfs_datastore.d.ts", "require": "./stores/message/ipfs_datastore.d.cts", "default": "./stores/message/ipfs_datastore.d.ts"}, "import": "./stores/message/ipfs_datastore.js", "require": "./stores/message/ipfs_datastore.cjs"}, "./stores/message/ioredis": {"types": {"import": "./stores/message/ioredis.d.ts", "require": "./stores/message/ioredis.d.cts", "default": "./stores/message/ioredis.d.ts"}, "import": "./stores/message/ioredis.js", "require": "./stores/message/ioredis.cjs"}, "./stores/message/momento": {"types": {"import": "./stores/message/momento.d.ts", "require": "./stores/message/momento.d.cts", "default": "./stores/message/momento.d.ts"}, "import": "./stores/message/momento.js", "require": "./stores/message/momento.cjs"}, "./stores/message/mongodb": {"types": {"import": "./stores/message/mongodb.d.ts", "require": "./stores/message/mongodb.d.cts", "default": "./stores/message/mongodb.d.ts"}, "import": "./stores/message/mongodb.js", "require": "./stores/message/mongodb.cjs"}, "./stores/message/planetscale": {"types": {"import": "./stores/message/planetscale.d.ts", "require": "./stores/message/planetscale.d.cts", "default": "./stores/message/planetscale.d.ts"}, "import": "./stores/message/planetscale.js", "require": "./stores/message/planetscale.cjs"}, "./stores/message/postgres": {"types": {"import": "./stores/message/postgres.d.ts", "require": "./stores/message/postgres.d.cts", "default": "./stores/message/postgres.d.ts"}, "import": "./stores/message/postgres.js", "require": "./stores/message/postgres.cjs"}, "./stores/message/redis": {"types": {"import": "./stores/message/redis.d.ts", "require": "./stores/message/redis.d.cts", "default": "./stores/message/redis.d.ts"}, "import": "./stores/message/redis.js", "require": "./stores/message/redis.cjs"}, "./stores/message/upstash_redis": {"types": {"import": "./stores/message/upstash_redis.d.ts", "require": "./stores/message/upstash_redis.d.cts", "default": "./stores/message/upstash_redis.d.ts"}, "import": "./stores/message/upstash_redis.js", "require": "./stores/message/upstash_redis.cjs"}, "./stores/message/xata": {"types": {"import": "./stores/message/xata.d.ts", "require": "./stores/message/xata.d.cts", "default": "./stores/message/xata.d.ts"}, "import": "./stores/message/xata.js", "require": "./stores/message/xata.cjs"}, "./memory/chat_memory": {"types": {"import": "./memory/chat_memory.d.ts", "require": "./memory/chat_memory.d.cts", "default": "./memory/chat_memory.d.ts"}, "import": "./memory/chat_memory.js", "require": "./memory/chat_memory.cjs"}, "./memory/motorhead_memory": {"types": {"import": "./memory/motorhead_memory.d.ts", "require": "./memory/motorhead_memory.d.cts", "default": "./memory/motorhead_memory.d.ts"}, "import": "./memory/motorhead_memory.js", "require": "./memory/motorhead_memory.cjs"}, "./memory/zep": {"types": {"import": "./memory/zep.d.ts", "require": "./memory/zep.d.cts", "default": "./memory/zep.d.ts"}, "import": "./memory/zep.js", "require": "./memory/zep.cjs"}, "./indexes/base": {"types": {"import": "./indexes/base.d.ts", "require": "./indexes/base.d.cts", "default": "./indexes/base.d.ts"}, "import": "./indexes/base.js", "require": "./indexes/base.cjs"}, "./indexes/postgres": {"types": {"import": "./indexes/postgres.d.ts", "require": "./indexes/postgres.d.cts", "default": "./indexes/postgres.d.ts"}, "import": "./indexes/postgres.js", "require": "./indexes/postgres.cjs"}, "./indexes/memory": {"types": {"import": "./indexes/memory.d.ts", "require": "./indexes/memory.d.cts", "default": "./indexes/memory.d.ts"}, "import": "./indexes/memory.js", "require": "./indexes/memory.cjs"}, "./indexes/sqlite": {"types": {"import": "./indexes/sqlite.d.ts", "require": "./indexes/sqlite.d.cts", "default": "./indexes/sqlite.d.ts"}, "import": "./indexes/sqlite.js", "require": "./indexes/sqlite.cjs"}, "./util/convex": {"types": {"import": "./util/convex.d.ts", "require": "./util/convex.d.cts", "default": "./util/convex.d.ts"}, "import": "./util/convex.js", "require": "./util/convex.cjs"}, "./utils/event_source_parse": {"types": {"import": "./utils/event_source_parse.d.ts", "require": "./utils/event_source_parse.d.cts", "default": "./utils/event_source_parse.d.ts"}, "import": "./utils/event_source_parse.js", "require": "./utils/event_source_parse.cjs"}, "./utils/cassandra": {"types": {"import": "./utils/cassandra.d.ts", "require": "./utils/cassandra.d.cts", "default": "./utils/cassandra.d.ts"}, "import": "./utils/cassandra.js", "require": "./utils/cassandra.cjs"}, "./package.json": "./package.json"}, "files": ["dist/", "load.cjs", "load.js", "load.d.ts", "load.d.cts", "load/serializable.cjs", "load/serializable.js", "load/serializable.d.ts", "load/serializable.d.cts", "tools/aiplugin.cjs", "tools/aiplugin.js", "tools/aiplugin.d.ts", "tools/aiplugin.d.cts", "tools/aws_lambda.cjs", "tools/aws_lambda.js", "tools/aws_lambda.d.ts", "tools/aws_lambda.d.cts", "tools/aws_sfn.cjs", "tools/aws_sfn.js", "tools/aws_sfn.d.ts", "tools/aws_sfn.d.cts", "tools/bingserpapi.cjs", "tools/bingserpapi.js", "tools/bingserpapi.d.ts", "tools/bingserpapi.d.cts", "tools/brave_search.cjs", "tools/brave_search.js", "tools/brave_search.d.ts", "tools/brave_search.d.cts", "tools/calculator.cjs", "tools/calculator.js", "tools/calculator.d.ts", "tools/calculator.d.cts", "tools/connery.cjs", "tools/connery.js", "tools/connery.d.ts", "tools/connery.d.cts", "tools/dadjokeapi.cjs", "tools/dadjokeapi.js", "tools/dadjokeapi.d.ts", "tools/dadjokeapi.d.cts", "tools/discord.cjs", "tools/discord.js", "tools/discord.d.ts", "tools/discord.d.cts", "tools/dynamic.cjs", "tools/dynamic.js", "tools/dynamic.d.ts", "tools/dynamic.d.cts", "tools/dataforseo_api_search.cjs", "tools/dataforseo_api_search.js", "tools/dataforseo_api_search.d.ts", "tools/dataforseo_api_search.d.cts", "tools/gmail.cjs", "tools/gmail.js", "tools/gmail.d.ts", "tools/gmail.d.cts", "tools/google_calendar.cjs", "tools/google_calendar.js", "tools/google_calendar.d.ts", "tools/google_calendar.d.cts", "tools/google_custom_search.cjs", "tools/google_custom_search.js", "tools/google_custom_search.d.ts", "tools/google_custom_search.d.cts", "tools/google_places.cjs", "tools/google_places.js", "tools/google_places.d.ts", "tools/google_places.d.cts", "tools/ifttt.cjs", "tools/ifttt.js", "tools/ifttt.d.ts", "tools/ifttt.d.cts", "tools/searchapi.cjs", "tools/searchapi.js", "tools/searchapi.d.ts", "tools/searchapi.d.cts", "tools/searxng_search.cjs", "tools/searxng_search.js", "tools/searxng_search.d.ts", "tools/searxng_search.d.cts", "tools/serpapi.cjs", "tools/serpapi.js", "tools/serpapi.d.ts", "tools/serpapi.d.cts", "tools/serper.cjs", "tools/serper.js", "tools/serper.d.ts", "tools/serper.d.cts", "tools/tavily_search.cjs", "tools/tavily_search.js", "tools/tavily_search.d.ts", "tools/tavily_search.d.cts", "tools/wikipedia_query_run.cjs", "tools/wikipedia_query_run.js", "tools/wikipedia_query_run.d.ts", "tools/wikipedia_query_run.d.cts", "tools/wolframalpha.cjs", "tools/wolframalpha.js", "tools/wolframalpha.d.ts", "tools/wolframalpha.d.cts", "agents/toolkits/aws_sfn.cjs", "agents/toolkits/aws_sfn.js", "agents/toolkits/aws_sfn.d.ts", "agents/toolkits/aws_sfn.d.cts", "agents/toolkits/base.cjs", "agents/toolkits/base.js", "agents/toolkits/base.d.ts", "agents/toolkits/base.d.cts", "agents/toolkits/connery.cjs", "agents/toolkits/connery.js", "agents/toolkits/connery.d.ts", "agents/toolkits/connery.d.cts", "embeddings/alibaba_tongyi.cjs", "embeddings/alibaba_tongyi.js", "embeddings/alibaba_tongyi.d.ts", "embeddings/alibaba_tongyi.d.cts", "embeddings/bedrock.cjs", "embeddings/bedrock.js", "embeddings/bedrock.d.ts", "embeddings/bedrock.d.cts", "embeddings/cloudflare_workersai.cjs", "embeddings/cloudflare_workersai.js", "embeddings/cloudflare_workersai.d.ts", "embeddings/cloudflare_workersai.d.cts", "embeddings/cohere.cjs", "embeddings/cohere.js", "embeddings/cohere.d.ts", "embeddings/cohere.d.cts", "embeddings/fireworks.cjs", "embeddings/fireworks.js", "embeddings/fireworks.d.ts", "embeddings/fireworks.d.cts", "embeddings/googlepalm.cjs", "embeddings/googlepalm.js", "embeddings/googlepalm.d.ts", "embeddings/googlepalm.d.cts", "embeddings/googlevertexai.cjs", "embeddings/googlevertexai.js", "embeddings/googlevertexai.d.ts", "embeddings/googlevertexai.d.cts", "embeddings/gradient_ai.cjs", "embeddings/gradient_ai.js", "embeddings/gradient_ai.d.ts", "embeddings/gradient_ai.d.cts", "embeddings/hf.cjs", "embeddings/hf.js", "embeddings/hf.d.ts", "embeddings/hf.d.cts", "embeddings/hf_transformers.cjs", "embeddings/hf_transformers.js", "embeddings/hf_transformers.d.ts", "embeddings/hf_transformers.d.cts", "embeddings/llama_cpp.cjs", "embeddings/llama_cpp.js", "embeddings/llama_cpp.d.ts", "embeddings/llama_cpp.d.cts", "embeddings/minimax.cjs", "embeddings/minimax.js", "embeddings/minimax.d.ts", "embeddings/minimax.d.cts", "embeddings/ollama.cjs", "embeddings/ollama.js", "embeddings/ollama.d.ts", "embeddings/ollama.d.cts", "embeddings/premai.cjs", "embeddings/premai.js", "embeddings/premai.d.ts", "embeddings/premai.d.cts", "embeddings/tensorflow.cjs", "embeddings/tensorflow.js", "embeddings/tensorflow.d.ts", "embeddings/tensorflow.d.cts", "embeddings/togetherai.cjs", "embeddings/togetherai.js", "embeddings/togetherai.d.ts", "embeddings/togetherai.d.cts", "embeddings/voyage.cjs", "embeddings/voyage.js", "embeddings/voyage.d.ts", "embeddings/voyage.d.cts", "embeddings/zhipuai.cjs", "embeddings/zhipuai.js", "embeddings/zhipuai.d.ts", "embeddings/zhipuai.d.cts", "llms/ai21.cjs", "llms/ai21.js", "llms/ai21.d.ts", "llms/ai21.d.cts", "llms/aleph_alpha.cjs", "llms/aleph_alpha.js", "llms/aleph_alpha.d.ts", "llms/aleph_alpha.d.cts", "llms/bedrock.cjs", "llms/bedrock.js", "llms/bedrock.d.ts", "llms/bedrock.d.cts", "llms/bedrock/web.cjs", "llms/bedrock/web.js", "llms/bedrock/web.d.ts", "llms/bedrock/web.d.cts", "llms/cloudflare_workersai.cjs", "llms/cloudflare_workersai.js", "llms/cloudflare_workersai.d.ts", "llms/cloudflare_workersai.d.cts", "llms/cohere.cjs", "llms/cohere.js", "llms/cohere.d.ts", "llms/cohere.d.cts", "llms/fireworks.cjs", "llms/fireworks.js", "llms/fireworks.d.ts", "llms/fireworks.d.cts", "llms/googlepalm.cjs", "llms/googlepalm.js", "llms/googlepalm.d.ts", "llms/googlepalm.d.cts", "llms/googlevertexai.cjs", "llms/googlevertexai.js", "llms/googlevertexai.d.ts", "llms/googlevertexai.d.cts", "llms/googlevertexai/web.cjs", "llms/googlevertexai/web.js", "llms/googlevertexai/web.d.ts", "llms/googlevertexai/web.d.cts", "llms/gradient_ai.cjs", "llms/gradient_ai.js", "llms/gradient_ai.d.ts", "llms/gradient_ai.d.cts", "llms/hf.cjs", "llms/hf.js", "llms/hf.d.ts", "llms/hf.d.cts", "llms/llama_cpp.cjs", "llms/llama_cpp.js", "llms/llama_cpp.d.ts", "llms/llama_cpp.d.cts", "llms/ollama.cjs", "llms/ollama.js", "llms/ollama.d.ts", "llms/ollama.d.cts", "llms/portkey.cjs", "llms/portkey.js", "llms/portkey.d.ts", "llms/portkey.d.cts", "llms/raycast.cjs", "llms/raycast.js", "llms/raycast.d.ts", "llms/raycast.d.cts", "llms/replicate.cjs", "llms/replicate.js", "llms/replicate.d.ts", "llms/replicate.d.cts", "llms/sagemaker_endpoint.cjs", "llms/sagemaker_endpoint.js", "llms/sagemaker_endpoint.d.ts", "llms/sagemaker_endpoint.d.cts", "llms/togetherai.cjs", "llms/togetherai.js", "llms/togetherai.d.ts", "llms/togetherai.d.cts", "llms/watsonx_ai.cjs", "llms/watsonx_ai.js", "llms/watsonx_ai.d.ts", "llms/watsonx_ai.d.cts", "llms/writer.cjs", "llms/writer.js", "llms/writer.d.ts", "llms/writer.d.cts", "llms/yandex.cjs", "llms/yandex.js", "llms/yandex.d.ts", "llms/yandex.d.cts", "vectorstores/analyticdb.cjs", "vectorstores/analyticdb.js", "vectorstores/analyticdb.d.ts", "vectorstores/analyticdb.d.cts", "vectorstores/astradb.cjs", "vectorstores/astradb.js", "vectorstores/astradb.d.ts", "vectorstores/astradb.d.cts", "vectorstores/azure_aisearch.cjs", "vectorstores/azure_aisearch.js", "vectorstores/azure_aisearch.d.ts", "vectorstores/azure_aisearch.d.cts", "vectorstores/azure_cosmosdb.cjs", "vectorstores/azure_cosmosdb.js", "vectorstores/azure_cosmosdb.d.ts", "vectorstores/azure_cosmosdb.d.cts", "vectorstores/cassandra.cjs", "vectorstores/cassandra.js", "vectorstores/cassandra.d.ts", "vectorstores/cassandra.d.cts", "vectorstores/chroma.cjs", "vectorstores/chroma.js", "vectorstores/chroma.d.ts", "vectorstores/chroma.d.cts", "vectorstores/clickhouse.cjs", "vectorstores/clickhouse.js", "vectorstores/clickhouse.d.ts", "vectorstores/clickhouse.d.cts", "vectorstores/closevector/node.cjs", "vectorstores/closevector/node.js", "vectorstores/closevector/node.d.ts", "vectorstores/closevector/node.d.cts", "vectorstores/closevector/web.cjs", "vectorstores/closevector/web.js", "vectorstores/closevector/web.d.ts", "vectorstores/closevector/web.d.cts", "vectorstores/cloudflare_vectorize.cjs", "vectorstores/cloudflare_vectorize.js", "vectorstores/cloudflare_vectorize.d.ts", "vectorstores/cloudflare_vectorize.d.cts", "vectorstores/convex.cjs", "vectorstores/convex.js", "vectorstores/convex.d.ts", "vectorstores/convex.d.cts", "vectorstores/couchbase.cjs", "vectorstores/couchbase.js", "vectorstores/couchbase.d.ts", "vectorstores/couchbase.d.cts", "vectorstores/elasticsearch.cjs", "vectorstores/elasticsearch.js", "vectorstores/elasticsearch.d.ts", "vectorstores/elasticsearch.d.cts", "vectorstores/faiss.cjs", "vectorstores/faiss.js", "vectorstores/faiss.d.ts", "vectorstores/faiss.d.cts", "vectorstores/googlevertexai.cjs", "vectorstores/googlevertexai.js", "vectorstores/googlevertexai.d.ts", "vectorstores/googlevertexai.d.cts", "vectorstores/hnswlib.cjs", "vectorstores/hnswlib.js", "vectorstores/hnswlib.d.ts", "vectorstores/hnswlib.d.cts", "vectorstores/lancedb.cjs", "vectorstores/lancedb.js", "vectorstores/lancedb.d.ts", "vectorstores/lancedb.d.cts", "vectorstores/milvus.cjs", "vectorstores/milvus.js", "vectorstores/milvus.d.ts", "vectorstores/milvus.d.cts", "vectorstores/momento_vector_index.cjs", "vectorstores/momento_vector_index.js", "vectorstores/momento_vector_index.d.ts", "vectorstores/momento_vector_index.d.cts", "vectorstores/mongodb_atlas.cjs", "vectorstores/mongodb_atlas.js", "vectorstores/mongodb_atlas.d.ts", "vectorstores/mongodb_atlas.d.cts", "vectorstores/myscale.cjs", "vectorstores/myscale.js", "vectorstores/myscale.d.ts", "vectorstores/myscale.d.cts", "vectorstores/neo4j_vector.cjs", "vectorstores/neo4j_vector.js", "vectorstores/neo4j_vector.d.ts", "vectorstores/neo4j_vector.d.cts", "vectorstores/opensearch.cjs", "vectorstores/opensearch.js", "vectorstores/opensearch.d.ts", "vectorstores/opensearch.d.cts", "vectorstores/pgvector.cjs", "vectorstores/pgvector.js", "vectorstores/pgvector.d.ts", "vectorstores/pgvector.d.cts", "vectorstores/pinecone.cjs", "vectorstores/pinecone.js", "vectorstores/pinecone.d.ts", "vectorstores/pinecone.d.cts", "vectorstores/prisma.cjs", "vectorstores/prisma.js", "vectorstores/prisma.d.ts", "vectorstores/prisma.d.cts", "vectorstores/qdrant.cjs", "vectorstores/qdrant.js", "vectorstores/qdrant.d.ts", "vectorstores/qdrant.d.cts", "vectorstores/redis.cjs", "vectorstores/redis.js", "vectorstores/redis.d.ts", "vectorstores/redis.d.cts", "vectorstores/rockset.cjs", "vectorstores/rockset.js", "vectorstores/rockset.d.ts", "vectorstores/rockset.d.cts", "vectorstores/singlestore.cjs", "vectorstores/singlestore.js", "vectorstores/singlestore.d.ts", "vectorstores/singlestore.d.cts", "vectorstores/supabase.cjs", "vectorstores/supabase.js", "vectorstores/supabase.d.ts", "vectorstores/supabase.d.cts", "vectorstores/tigris.cjs", "vectorstores/tigris.js", "vectorstores/tigris.d.ts", "vectorstores/tigris.d.cts", "vectorstores/turbopuffer.cjs", "vectorstores/turbopuffer.js", "vectorstores/turbopuffer.d.ts", "vectorstores/turbopuffer.d.cts", "vectorstores/typeorm.cjs", "vectorstores/typeorm.js", "vectorstores/typeorm.d.ts", "vectorstores/typeorm.d.cts", "vectorstores/typesense.cjs", "vectorstores/typesense.js", "vectorstores/typesense.d.ts", "vectorstores/typesense.d.cts", "vectorstores/upstash.cjs", "vectorstores/upstash.js", "vectorstores/upstash.d.ts", "vectorstores/upstash.d.cts", "vectorstores/usearch.cjs", "vectorstores/usearch.js", "vectorstores/usearch.d.ts", "vectorstores/usearch.d.cts", "vectorstores/vectara.cjs", "vectorstores/vectara.js", "vectorstores/vectara.d.ts", "vectorstores/vectara.d.cts", "vectorstores/vercel_postgres.cjs", "vectorstores/vercel_postgres.js", "vectorstores/vercel_postgres.d.ts", "vectorstores/vercel_postgres.d.cts", "vectorstores/voy.cjs", "vectorstores/voy.js", "vectorstores/voy.d.ts", "vectorstores/voy.d.cts", "vectorstores/weaviate.cjs", "vectorstores/weaviate.js", "vectorstores/weaviate.d.ts", "vectorstores/weaviate.d.cts", "vectorstores/xata.cjs", "vectorstores/xata.js", "vectorstores/xata.d.ts", "vectorstores/xata.d.cts", "vectorstores/zep.cjs", "vectorstores/zep.js", "vectorstores/zep.d.ts", "vectorstores/zep.d.cts", "chat_models/alibaba_tongyi.cjs", "chat_models/alibaba_tongyi.js", "chat_models/alibaba_tongyi.d.ts", "chat_models/alibaba_tongyi.d.cts", "chat_models/baiduwenxin.cjs", "chat_models/baiduwenxin.js", "chat_models/baiduwenxin.d.ts", "chat_models/baiduwenxin.d.cts", "chat_models/bedrock.cjs", "chat_models/bedrock.js", "chat_models/bedrock.d.ts", "chat_models/bedrock.d.cts", "chat_models/bedrock/web.cjs", "chat_models/bedrock/web.js", "chat_models/bedrock/web.d.ts", "chat_models/bedrock/web.d.cts", "chat_models/cloudflare_workersai.cjs", "chat_models/cloudflare_workersai.js", "chat_models/cloudflare_workersai.d.ts", "chat_models/cloudflare_workersai.d.cts", "chat_models/fireworks.cjs", "chat_models/fireworks.js", "chat_models/fireworks.d.ts", "chat_models/fireworks.d.cts", "chat_models/googlevertexai.cjs", "chat_models/googlevertexai.js", "chat_models/googlevertexai.d.ts", "chat_models/googlevertexai.d.cts", "chat_models/googlevertexai/web.cjs", "chat_models/googlevertexai/web.js", "chat_models/googlevertexai/web.d.ts", "chat_models/googlevertexai/web.d.cts", "chat_models/googlepalm.cjs", "chat_models/googlepalm.js", "chat_models/googlepalm.d.ts", "chat_models/googlepalm.d.cts", "chat_models/iflytek_xinghuo.cjs", "chat_models/iflytek_xinghuo.js", "chat_models/iflytek_xinghuo.d.ts", "chat_models/iflytek_xinghuo.d.cts", "chat_models/iflytek_xinghuo/web.cjs", "chat_models/iflytek_xinghuo/web.js", "chat_models/iflytek_xinghuo/web.d.ts", "chat_models/iflytek_xinghuo/web.d.cts", "chat_models/llama_cpp.cjs", "chat_models/llama_cpp.js", "chat_models/llama_cpp.d.ts", "chat_models/llama_cpp.d.cts", "chat_models/minimax.cjs", "chat_models/minimax.js", "chat_models/minimax.d.ts", "chat_models/minimax.d.cts", "chat_models/ollama.cjs", "chat_models/ollama.js", "chat_models/ollama.d.ts", "chat_models/ollama.d.cts", "chat_models/portkey.cjs", "chat_models/portkey.js", "chat_models/portkey.d.ts", "chat_models/portkey.d.cts", "chat_models/premai.cjs", "chat_models/premai.js", "chat_models/premai.d.ts", "chat_models/premai.d.cts", "chat_models/togetherai.cjs", "chat_models/togetherai.js", "chat_models/togetherai.d.ts", "chat_models/togetherai.d.cts", "chat_models/yandex.cjs", "chat_models/yandex.js", "chat_models/yandex.d.ts", "chat_models/yandex.d.cts", "chat_models/zhipuai.cjs", "chat_models/zhipuai.js", "chat_models/zhipuai.d.ts", "chat_models/zhipuai.d.cts", "callbacks/handlers/llmonitor.cjs", "callbacks/handlers/llmonitor.js", "callbacks/handlers/llmonitor.d.ts", "callbacks/handlers/llmonitor.d.cts", "callbacks/handlers/lunary.cjs", "callbacks/handlers/lunary.js", "callbacks/handlers/lunary.d.ts", "callbacks/handlers/lunary.d.cts", "retrievers/amazon_kendra.cjs", "retrievers/amazon_kendra.js", "retrievers/amazon_kendra.d.ts", "retrievers/amazon_kendra.d.cts", "retrievers/amazon_knowledge_base.cjs", "retrievers/amazon_knowledge_base.js", "retrievers/amazon_knowledge_base.d.ts", "retrievers/amazon_knowledge_base.d.cts", "retrievers/chaindesk.cjs", "retrievers/chaindesk.js", "retrievers/chaindesk.d.ts", "retrievers/chaindesk.d.cts", "retrievers/databerry.cjs", "retrievers/databerry.js", "retrievers/databerry.d.ts", "retrievers/databerry.d.cts", "retrievers/dria.cjs", "retrievers/dria.js", "retrievers/dria.d.ts", "retrievers/dria.d.cts", "retrievers/metal.cjs", "retrievers/metal.js", "retrievers/metal.d.ts", "retrievers/metal.d.cts", "retrievers/remote.cjs", "retrievers/remote.js", "retrievers/remote.d.ts", "retrievers/remote.d.cts", "retrievers/supabase.cjs", "retrievers/supabase.js", "retrievers/supabase.d.ts", "retrievers/supabase.d.cts", "retrievers/tavily_search_api.cjs", "retrievers/tavily_search_api.js", "retrievers/tavily_search_api.d.ts", "retrievers/tavily_search_api.d.cts", "retrievers/vectara_summary.cjs", "retrievers/vectara_summary.js", "retrievers/vectara_summary.d.ts", "retrievers/vectara_summary.d.cts", "retrievers/vespa.cjs", "retrievers/vespa.js", "retrievers/vespa.d.ts", "retrievers/vespa.d.cts", "retrievers/zep.cjs", "retrievers/zep.js", "retrievers/zep.d.ts", "retrievers/zep.d.cts", "caches/cloudflare_kv.cjs", "caches/cloudflare_kv.js", "caches/cloudflare_kv.d.ts", "caches/cloudflare_kv.d.cts", "caches/ioredis.cjs", "caches/ioredis.js", "caches/ioredis.d.ts", "caches/ioredis.d.cts", "caches/momento.cjs", "caches/momento.js", "caches/momento.d.ts", "caches/momento.d.cts", "caches/upstash_redis.cjs", "caches/upstash_redis.js", "caches/upstash_redis.d.ts", "caches/upstash_redis.d.cts", "graphs/neo4j_graph.cjs", "graphs/neo4j_graph.js", "graphs/neo4j_graph.d.ts", "graphs/neo4j_graph.d.cts", "graphs/memgraph_graph.cjs", "graphs/memgraph_graph.js", "graphs/memgraph_graph.d.ts", "graphs/memgraph_graph.d.cts", "document_transformers/html_to_text.cjs", "document_transformers/html_to_text.js", "document_transformers/html_to_text.d.ts", "document_transformers/html_to_text.d.cts", "document_transformers/mozilla_readability.cjs", "document_transformers/mozilla_readability.js", "document_transformers/mozilla_readability.d.ts", "document_transformers/mozilla_readability.d.cts", "storage/cassandra.cjs", "storage/cassandra.js", "storage/cassandra.d.ts", "storage/cassandra.d.cts", "storage/convex.cjs", "storage/convex.js", "storage/convex.d.ts", "storage/convex.d.cts", "storage/ioredis.cjs", "storage/ioredis.js", "storage/ioredis.d.ts", "storage/ioredis.d.cts", "storage/upstash_redis.cjs", "storage/upstash_redis.js", "storage/upstash_redis.d.ts", "storage/upstash_redis.d.cts", "storage/vercel_kv.cjs", "storage/vercel_kv.js", "storage/vercel_kv.d.ts", "storage/vercel_kv.d.cts", "stores/doc/base.cjs", "stores/doc/base.js", "stores/doc/base.d.ts", "stores/doc/base.d.cts", "stores/doc/in_memory.cjs", "stores/doc/in_memory.js", "stores/doc/in_memory.d.ts", "stores/doc/in_memory.d.cts", "stores/message/astradb.cjs", "stores/message/astradb.js", "stores/message/astradb.d.ts", "stores/message/astradb.d.cts", "stores/message/cassandra.cjs", "stores/message/cassandra.js", "stores/message/cassandra.d.ts", "stores/message/cassandra.d.cts", "stores/message/cloudflare_d1.cjs", "stores/message/cloudflare_d1.js", "stores/message/cloudflare_d1.d.ts", "stores/message/cloudflare_d1.d.cts", "stores/message/convex.cjs", "stores/message/convex.js", "stores/message/convex.d.ts", "stores/message/convex.d.cts", "stores/message/dynamodb.cjs", "stores/message/dynamodb.js", "stores/message/dynamodb.d.ts", "stores/message/dynamodb.d.cts", "stores/message/firestore.cjs", "stores/message/firestore.js", "stores/message/firestore.d.ts", "stores/message/firestore.d.cts", "stores/message/in_memory.cjs", "stores/message/in_memory.js", "stores/message/in_memory.d.ts", "stores/message/in_memory.d.cts", "stores/message/ipfs_datastore.cjs", "stores/message/ipfs_datastore.js", "stores/message/ipfs_datastore.d.ts", "stores/message/ipfs_datastore.d.cts", "stores/message/ioredis.cjs", "stores/message/ioredis.js", "stores/message/ioredis.d.ts", "stores/message/ioredis.d.cts", "stores/message/momento.cjs", "stores/message/momento.js", "stores/message/momento.d.ts", "stores/message/momento.d.cts", "stores/message/mongodb.cjs", "stores/message/mongodb.js", "stores/message/mongodb.d.ts", "stores/message/mongodb.d.cts", "stores/message/planetscale.cjs", "stores/message/planetscale.js", "stores/message/planetscale.d.ts", "stores/message/planetscale.d.cts", "stores/message/postgres.cjs", "stores/message/postgres.js", "stores/message/postgres.d.ts", "stores/message/postgres.d.cts", "stores/message/redis.cjs", "stores/message/redis.js", "stores/message/redis.d.ts", "stores/message/redis.d.cts", "stores/message/upstash_redis.cjs", "stores/message/upstash_redis.js", "stores/message/upstash_redis.d.ts", "stores/message/upstash_redis.d.cts", "stores/message/xata.cjs", "stores/message/xata.js", "stores/message/xata.d.ts", "stores/message/xata.d.cts", "memory/chat_memory.cjs", "memory/chat_memory.js", "memory/chat_memory.d.ts", "memory/chat_memory.d.cts", "memory/motorhead_memory.cjs", "memory/motorhead_memory.js", "memory/motorhead_memory.d.ts", "memory/motorhead_memory.d.cts", "memory/zep.cjs", "memory/zep.js", "memory/zep.d.ts", "memory/zep.d.cts", "indexes/base.cjs", "indexes/base.js", "indexes/base.d.ts", "indexes/base.d.cts", "indexes/postgres.cjs", "indexes/postgres.js", "indexes/postgres.d.ts", "indexes/postgres.d.cts", "indexes/memory.cjs", "indexes/memory.js", "indexes/memory.d.ts", "indexes/memory.d.cts", "indexes/sqlite.cjs", "indexes/sqlite.js", "indexes/sqlite.d.ts", "indexes/sqlite.d.cts", "util/convex.cjs", "util/convex.js", "util/convex.d.ts", "util/convex.d.cts", "utils/event_source_parse.cjs", "utils/event_source_parse.js", "utils/event_source_parse.d.ts", "utils/event_source_parse.d.cts", "utils/cassandra.cjs", "utils/cassandra.js", "utils/cassandra.d.ts", "utils/cassandra.d.cts"]}