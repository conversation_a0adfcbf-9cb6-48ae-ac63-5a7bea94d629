import request from 'supertest';
import Server from '../src/server.js';

describe('Chat API', () => {
  let server;
  let app;

  beforeAll(async () => {
    server = new Server();
    app = await server.start();
  });

  afterAll(async () => {
    await server.shutdown();
  });

  describe('POST /api/chat', () => {
    test('should handle basic chat message', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'Hello, I need help with travel planning'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.sessionId).toBeDefined();
      expect(response.body.response).toBeDefined();
      expect(response.body.state).toBeDefined();
    });

    test('should validate message length', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: ''
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid request');
    });

    test('should handle hotel search request', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'I need to find a hotel in New York for 2 guests from 2024-12-01 to 2024-12-03'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.response).toContain('hotel');
    });

    test('should handle flight search request', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'I want to book a flight from New York to Los Angeles on 2024-12-01 for 1 passenger'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.response).toContain('flight');
    });

    test('should maintain session continuity', async () => {
      // First message
      const response1 = await request(app)
        .post('/api/chat')
        .send({
          message: 'I need a hotel'
        });

      const sessionId = response1.body.sessionId;

      // Second message with same session
      const response2 = await request(app)
        .post('/api/chat')
        .send({
          message: 'In Paris',
          sessionId: sessionId
        });

      expect(response2.status).toBe(200);
      expect(response2.body.sessionId).toBe(sessionId);
    });
  });

  describe('GET /api/chat/:sessionId/state', () => {
    test('should return conversation state', async () => {
      // Create a conversation first
      const chatResponse = await request(app)
        .post('/api/chat')
        .send({
          message: 'Hello'
        });

      const sessionId = chatResponse.body.sessionId;

      // Get state
      const response = await request(app)
        .get(`/api/chat/${sessionId}/state`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.state).toBeDefined();
      expect(response.body.sessionId).toBe(sessionId);
    });

    test('should return 404 for non-existent session', async () => {
      const response = await request(app)
        .get('/api/chat/00000000-0000-0000-0000-000000000000/state');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/chat/:sessionId/reset', () => {
    test('should reset conversation', async () => {
      // Create a conversation first
      const chatResponse = await request(app)
        .post('/api/chat')
        .send({
          message: 'Hello'
        });

      const sessionId = chatResponse.body.sessionId;

      // Reset conversation
      const response = await request(app)
        .post(`/api/chat/${sessionId}/reset`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.sessionId).toBe(sessionId);
    });
  });

  describe('GET /api/health', () => {
    test('should return health status', async () => {
      const response = await request(app)
        .get('/api/health');

      expect(response.status).toBeOneOf([200, 503]);
      expect(response.body.status).toBeDefined();
      expect(response.body.services).toBeDefined();
    });
  });

  describe('GET /api/docs', () => {
    test('should return API documentation', async () => {
      const response = await request(app)
        .get('/api/docs');

      expect(response.status).toBe(200);
      expect(response.body.title).toBeDefined();
      expect(response.body.endpoints).toBeDefined();
    });
  });

  describe('Error handling', () => {
    test('should handle invalid JSON', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send('invalid json')
        .set('Content-Type', 'application/json');

      expect(response.status).toBe(400);
    });

    test('should handle missing message', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should handle message too long', async () => {
      const longMessage = 'a'.repeat(2001);
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: longMessage
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });
});
