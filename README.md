# LLM Travel Chatbot

An intelligent chatbot for hotel and flight search powered by Claude Sonnet 4 via AWS Bedrock, using LangChain/LangGraph for conversation management and MCP (Model Context Protocol) for API integration.

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React Client  │───▶│  Chat API Server │───▶│  LangGraph      │
│   (External)    │    │  (Express.js)    │    │  State Machine  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  Claude Sonnet 4 │    │  MCP Server     │
                       │  (AWS Bedrock)   │    │  (API Gateway)  │
                       └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  Travel APIs    │
                                               │  (Hotels/Flights)│
                                               └─────────────────┘
```

## ✨ Features

- **🤖 Conversational AI**: Natural language interaction powered by Claude Sonnet 4
- **🏨 Hotel Search**: Comprehensive hotel search with filters and preferences
- **✈️ Flight Search**: Flight booking assistance with flexible options
- **💬 Context Management**: Maintains conversation state across interactions
- **🔄 Real-time API Integration**: Live data from travel service providers via MCP
- **🛡️ Error Handling**: Robust error handling and fallback mechanisms
- **📊 State Management**: LangGraph-powered conversation flow management
- **🔍 Parameter Extraction**: Intelligent extraction of travel parameters from natural language

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- AWS Account with Bedrock access
- AWS CLI configured or environment variables set

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd llm-travel-chatbot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your AWS credentials and configuration
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

The server will start on `http://localhost:3000` with the MCP server on `http://localhost:3001`.

## 📋 API Documentation

### Base URL
```
http://localhost:3000/api
```

### Endpoints

#### `POST /api/chat`
Send a chat message to the bot.

**Request Body:**
```json
{
  "message": "I need a hotel in Paris",
  "sessionId": "optional-uuid-v4"
}
```

**Response:**
```json
{
  "success": true,
  "sessionId": "123e4567-e89b-12d3-a456-************",
  "response": "I'd be happy to help you find a hotel in Paris! Could you please tell me your check-in date?",
  "state": {
    "phase": "parameter_collection",
    "serviceType": "hotel",
    "collectedParameters": {
      "destination": "Paris"
    },
    "missingParameters": ["checkInDate", "checkOutDate", "guests"]
  },
  "timestamp": "2024-07-06T10:30:00.000Z"
}
```

#### `GET /api/chat/:sessionId/state`
Get the current conversation state.

**Response:**
```json
{
  "success": true,
  "sessionId": "123e4567-e89b-12d3-a456-************",
  "state": {
    "phase": "parameter_collection",
    "serviceType": "hotel",
    "collectedParameters": {},
    "missingParameters": ["destination", "checkInDate", "checkOutDate", "guests"],
    "hasAllParameters": false,
    "messageCount": 2
  },
  "timestamp": "2024-07-06T10:30:00.000Z"
}
```

#### `POST /api/chat/:sessionId/reset`
Reset a conversation session.

**Response:**
```json
{
  "success": true,
  "sessionId": "123e4567-e89b-12d3-a456-************",
  "message": "Conversation reset successfully",
  "timestamp": "2024-07-06T10:30:00.000Z"
}
```

#### `GET /api/health`
Check the health status of the service.

**Response:**
```json
{
  "status": "healthy",
  "services": {
    "bedrock": "healthy",
    "mcp": "healthy"
  },
  "timestamp": "2024-07-06T10:30:00.000Z",
  "uptime": 3600
}
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-5-sonnet-20241022-v2:0

# Server Configuration
PORT=3000
NODE_ENV=development

# API Keys for Travel Services (optional for demo)
HOTEL_API_KEY=your_hotel_api_key_here
FLIGHT_API_KEY=your_flight_api_key_here

# MCP Configuration
MCP_SERVER_PORT=3001

# Logging
LOG_LEVEL=info
```

### AWS Bedrock Setup

1. **Enable Claude Sonnet 4 in AWS Bedrock**
   - Go to AWS Bedrock console
   - Navigate to "Model access"
   - Request access to Anthropic Claude models

2. **Configure AWS Credentials**
   - Use AWS CLI: `aws configure`
   - Or set environment variables as shown above
   - Or use IAM roles if running on EC2

## 💻 Development

### Available Scripts

```bash
# Start development server with hot reload
npm run dev

# Start production server
npm start

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run example usage
node examples/usage.js
```

### Project Structure

```
src/
├── config/           # Configuration files
├── controllers/      # API route controllers
├── services/         # Business logic services
├── models/          # Data models and schemas
├── utils/           # Utility functions
├── mcp/             # MCP server implementation
└── server.js        # Main server file

tests/               # Test files
examples/            # Usage examples
logs/               # Log files (created at runtime)
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm test -- --coverage
```

### Test Structure

- **Unit Tests**: Test individual components and services
- **Integration Tests**: Test API endpoints and service interactions
- **Mock Services**: AWS Bedrock and travel APIs are mocked for testing

## 📖 Usage Examples

### Basic Chat Interaction

```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:3000/api'
});

// Start a conversation
const response = await api.post('/chat', {
  message: 'Hello! I need help planning a trip.'
});

console.log(response.data.response);
// Output: "Hello! I'd be happy to help you plan your trip. Are you looking for hotel accommodations, flight bookings, or both?"
```

### Hotel Search Flow

```javascript
// 1. Express hotel need
let response = await api.post('/chat', {
  message: 'I need a hotel in Tokyo'
});

const sessionId = response.data.sessionId;

// 2. Provide dates
response = await api.post('/chat', {
  message: 'From January 15th to January 18th, 2025',
  sessionId: sessionId
});

// 3. Provide guest count
response = await api.post('/chat', {
  message: '2 guests',
  sessionId: sessionId
});

// Bot will now search for hotels and return results
if (response.data.apiData) {
  console.log('Hotels found:', response.data.apiData.hotels);
}
```

### Flight Search Flow

```javascript
// Complete flight request in one message
const response = await api.post('/chat', {
  message: 'Book a flight from New York to Los Angeles on March 1st, 2025 for 1 passenger'
});

if (response.data.apiData) {
  console.log('Flights found:', response.data.apiData.outboundFlights);
}
```

## 🔄 Conversation Flow

The chatbot follows a structured conversation flow managed by LangGraph:

1. **Greeting**: Welcome the user and understand their intent
2. **Service Selection**: Determine if they need hotel or flight search
3. **Parameter Collection**: Gather required information (dates, location, guests, etc.)
4. **API Call**: Execute search via MCP server
5. **Result Processing**: Present results through Claude Sonnet 4
6. **Follow-up**: Handle additional questions or refinements

### Conversation States

- `greeting`: Initial welcome state
- `service_selection`: Determining user's travel needs
- `parameter_collection`: Gathering search parameters
- `api_call`: Executing travel search
- `result_processing`: Presenting search results
- `follow_up`: Handling additional requests

## 🛠️ MCP (Model Context Protocol) Integration

The system uses MCP to bridge between the LLM and external APIs:

### Available Tools

- `search_hotels`: Search for hotels with parameters
- `search_flights`: Search for flights with parameters
- `get_hotel_details`: Get detailed hotel information
- `get_flight_details`: Get detailed flight information

### MCP Server Endpoints

- `POST /search/hotels`: Direct hotel search
- `POST /search/flights`: Direct flight search
- `POST /execute-tool`: Generic tool execution
- `GET /tools`: List available tools
- `GET /health`: MCP server health check

## 🚨 Error Handling

The system includes comprehensive error handling:

### Error Types

- **ValidationError**: Invalid input parameters
- **BedrockError**: AWS Bedrock service issues
- **MCPError**: MCP server communication problems
- **ConversationError**: Conversation state issues
- **RateLimitError**: API rate limiting

### Fallback Mechanisms

- Circuit breaker pattern for external services
- Graceful degradation when services are unavailable
- Retry logic with exponential backoff
- Mock data fallback for development/testing

## 🔒 Security Considerations

- Input validation and sanitization
- Rate limiting (implement as needed)
- AWS IAM roles for production deployment
- CORS configuration for frontend integration
- Request/response logging for monitoring

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**
   ```bash
   NODE_ENV=production
   PORT=3000
   # Set production AWS credentials
   ```

2. **Build and Start**
   ```bash
   npm install --production
   npm start
   ```

3. **Process Management** (recommended)
   ```bash
   # Using PM2
   npm install -g pm2
   pm2 start src/server.js --name "travel-chatbot"
   ```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Troubleshooting

### Common Issues

1. **AWS Bedrock Access Denied**
   - Ensure you have requested access to Claude models in AWS Bedrock console
   - Verify your AWS credentials have the necessary permissions

2. **MCP Server Connection Failed**
   - Check if MCP server is running on the configured port
   - Verify firewall settings allow local connections

3. **Tests Failing**
   - Ensure test environment variables are set
   - Check if all dependencies are installed

### Getting Help

- Check the logs in the `logs/` directory
- Use the health check endpoint: `GET /api/health`
- Run the example usage script: `node examples/usage.js`

For more detailed examples, see the `examples/` directory.
