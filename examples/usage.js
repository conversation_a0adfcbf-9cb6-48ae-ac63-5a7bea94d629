/**
 * Example usage of the LLM Travel Chatbot API
 * This file demonstrates how to interact with the chatbot API
 */

import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api';

// Create an axios instance for API calls
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000,
});

/**
 * Example: Basic chat interaction
 */
async function basicChatExample() {
  console.log('\n=== Basic Chat Example ===');
  
  try {
    const response = await api.post('/chat', {
      message: 'Hello! I need help planning a trip.'
    });

    console.log('Response:', response.data.response);
    console.log('Session ID:', response.data.sessionId);
    
    return response.data.sessionId;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Example: Hotel search conversation
 */
async function hotelSearchExample() {
  console.log('\n=== Hotel Search Example ===');
  
  try {
    // Start conversation
    let response = await api.post('/chat', {
      message: 'I need to find a hotel in Paris'
    });
    
    const sessionId = response.data.sessionId;
    console.log('Bot:', response.data.response);

    // Provide check-in date
    response = await api.post('/chat', {
      message: 'I want to check in on December 15th, 2024',
      sessionId: sessionId
    });
    console.log('Bot:', response.data.response);

    // Provide check-out date
    response = await api.post('/chat', {
      message: 'And check out on December 18th, 2024',
      sessionId: sessionId
    });
    console.log('Bot:', response.data.response);

    // Provide number of guests
    response = await api.post('/chat', {
      message: '2 guests',
      sessionId: sessionId
    });
    console.log('Bot:', response.data.response);

    // If all parameters are collected, the bot should trigger hotel search
    if (response.data.apiData) {
      console.log('Hotel search results:', JSON.stringify(response.data.apiData, null, 2));
    }

    return sessionId;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Example: Flight search conversation
 */
async function flightSearchExample() {
  console.log('\n=== Flight Search Example ===');
  
  try {
    // Start with complete flight request
    const response = await api.post('/chat', {
      message: 'I want to book a flight from New York to Los Angeles on January 15th, 2025 for 1 passenger'
    });
    
    console.log('Bot:', response.data.response);
    
    if (response.data.apiData) {
      console.log('Flight search results:', JSON.stringify(response.data.apiData, null, 2));
    }

    return response.data.sessionId;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Example: Get conversation state
 */
async function getConversationStateExample(sessionId) {
  console.log('\n=== Conversation State Example ===');
  
  try {
    const response = await api.get(`/chat/${sessionId}/state`);
    console.log('Conversation State:', JSON.stringify(response.data.state, null, 2));
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Example: Reset conversation
 */
async function resetConversationExample(sessionId) {
  console.log('\n=== Reset Conversation Example ===');
  
  try {
    const response = await api.post(`/chat/${sessionId}/reset`);
    console.log('Reset result:', response.data.message);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Example: Health check
 */
async function healthCheckExample() {
  console.log('\n=== Health Check Example ===');
  
  try {
    const response = await api.get('/health');
    console.log('Health Status:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Example: Error handling
 */
async function errorHandlingExample() {
  console.log('\n=== Error Handling Example ===');
  
  try {
    // Send invalid request
    await api.post('/chat', {
      message: '' // Empty message should cause validation error
    });
  } catch (error) {
    console.log('Expected validation error:', error.response.data);
  }

  try {
    // Request non-existent session
    await api.get('/chat/invalid-session-id/state');
  } catch (error) {
    console.log('Expected session not found error:', error.response.data);
  }
}

/**
 * Example: Complete conversation flow
 */
async function completeConversationFlow() {
  console.log('\n=== Complete Conversation Flow ===');
  
  try {
    // 1. Start conversation
    let response = await api.post('/chat', {
      message: 'Hi there!'
    });
    const sessionId = response.data.sessionId;
    console.log('1. Greeting:', response.data.response);

    // 2. Express travel intent
    response = await api.post('/chat', {
      message: 'I need to plan a business trip',
      sessionId: sessionId
    });
    console.log('2. Travel intent:', response.data.response);

    // 3. Specify hotel need
    response = await api.post('/chat', {
      message: 'I need a hotel in San Francisco',
      sessionId: sessionId
    });
    console.log('3. Hotel request:', response.data.response);

    // 4. Provide all details at once
    response = await api.post('/chat', {
      message: 'From March 10th to March 12th, 2025, for 1 guest, mid-range budget',
      sessionId: sessionId
    });
    console.log('4. Hotel details:', response.data.response);

    // 5. Check final state
    const stateResponse = await api.get(`/chat/${sessionId}/state`);
    console.log('5. Final state:', stateResponse.data.state);

    return sessionId;
  } catch (error) {
    console.error('Error in complete flow:', error.response?.data || error.message);
  }
}

/**
 * Run all examples
 */
async function runAllExamples() {
  console.log('🤖 LLM Travel Chatbot API Examples');
  console.log('=====================================');

  // Check if server is running
  try {
    await healthCheckExample();
  } catch (error) {
    console.error('❌ Server is not running. Please start the server first with: npm run dev');
    return;
  }

  // Run examples
  const sessionId1 = await basicChatExample();
  const sessionId2 = await hotelSearchExample();
  const sessionId3 = await flightSearchExample();
  
  if (sessionId1) {
    await getConversationStateExample(sessionId1);
    await resetConversationExample(sessionId1);
  }

  await errorHandlingExample();
  await completeConversationFlow();

  console.log('\n✅ All examples completed!');
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples().catch(console.error);
}

export {
  basicChatExample,
  hotelSearchExample,
  flightSearchExample,
  getConversationStateExample,
  resetConversationExample,
  healthCheckExample,
  errorHandlingExample,
  completeConversationFlow
};
