/**
 * True <PERSON>n + MCP Integration Demo
 * This shows how <PERSON> automatically calls MCP tools without manual orchestration
 */

import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000,
});

/**
 * Example: <PERSON> automatically calls tools from first message
 */
async function immediateToolCallingExample() {
  console.log('\n=== Immediate Tool Calling Example ===');
  console.log('<PERSON> will call MCP tools immediately if enough info is provided');
  
  try {
    const response = await api.post('/chat', {
      message: 'Find me hotels in Tokyo for 2 guests from March 15-18, 2025'
    });

    console.log('User:', 'Find me hotels in Tokyo for 2 guests from March 15-18, 2025');
    console.log('\n🤖 Claude Response:', response.data.response);
    
    if (response.data.toolCalls) {
      console.log('\n🔧 Tools Automatically Called:');
      response.data.toolCalls.forEach(call => {
        console.log(`- ${call.name}`);
        console.log(`  Args:`, JSON.stringify(call.args, null, 2));
      });
    } else {
      console.log('\n❌ No tools were called - this indicates an issue with the integration');
    }

    if (response.data.toolResults) {
      console.log('\n📊 Tool Results:');
      response.data.toolResults.forEach(result => {
        console.log(`- ${result.toolName}: ${result.success ? 'Success' : 'Failed'}`);
      });
    }

    return response.data.sessionId;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Example: Complete trip planning in one message
 */
async function oneMessageTripPlanningExample() {
  console.log('\n=== One Message Trip Planning Example ===');
  console.log('Claude should automatically use combined travel tool');
  
  try {
    const response = await api.post('/chat', {
      message: 'Plan my complete vacation to Paris from New York, departing April 10th returning April 17th, 2025, for 2 people'
    });

    console.log('User:', 'Plan my complete vacation to Paris from New York, departing April 10th returning April 17th, 2025, for 2 people');
    console.log('\n🤖 Claude Response:', response.data.response);
    
    if (response.data.toolCalls) {
      console.log('\n🔧 Tools Automatically Called:');
      response.data.toolCalls.forEach(call => {
        console.log(`- ${call.name}`);
        if (call.name === 'search_combined_travel') {
          console.log('  ✅ Correctly chose combined travel tool!');
        }
      });
    } else {
      console.log('\n❌ No tools were called - Claude should have called search_combined_travel');
    }

    if (response.data.apiData?.combinedSearch) {
      console.log('\n🎯 Combined Search Results:');
      console.log(`- Flights: ${response.data.apiData.flights?.outboundFlights?.length || 0} options`);
      console.log(`- Hotels: ${response.data.apiData.hotels?.hotels?.length || 0} options`);
    }

    return response.data.sessionId;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Example: Progressive tool calling in conversation
 */
async function progressiveToolCallingExample() {
  console.log('\n=== Progressive Tool Calling Example ===');
  console.log('Testing how Claude calls tools as more information becomes available');
  
  try {
    // Start with minimal info
    let response = await api.post('/chat', {
      message: 'I need help with travel to London'
    });
    
    const sessionId = response.data.sessionId;
    console.log('User:', 'I need help with travel to London');
    console.log('Claude:', response.data.response);
    
    if (response.data.toolCalls) {
      console.log('🔧 Tools called:', response.data.toolCalls.map(t => t.name).join(', '));
    } else {
      console.log('💬 No tools called yet - needs more info');
    }

    // Add more specific information
    response = await api.post('/chat', {
      message: 'I need both flights from Boston and hotels, traveling March 1-7, 2025 for 1 person',
      sessionId: sessionId
    });

    console.log('\nUser:', 'I need both flights from Boston and hotels, traveling March 1-7, 2025 for 1 person');
    console.log('Claude:', response.data.response);

    if (response.data.toolCalls) {
      console.log('\n🔧 Tools Automatically Called:');
      response.data.toolCalls.forEach(call => {
        console.log(`- ${call.name}`);
        if (call.name === 'search_combined_travel') {
          console.log('  ✅ Correctly chose combined travel tool!');
        }
      });
    } else {
      console.log('\n❌ No tools were called - Claude should have called search_combined_travel');
    }

    return sessionId;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Example: Testing tool selection intelligence
 */
async function toolSelectionIntelligenceExample() {
  console.log('\n=== Tool Selection Intelligence Example ===');
  console.log('Testing Claude\'s ability to choose the right tool for different requests');
  
  const testCases = [
    {
      message: 'Find hotels in Miami for 2 guests March 20-23, 2025',
      expectedTool: 'search_hotels'
    },
    {
      message: 'Book a flight from Chicago to Seattle on April 5th for 1 passenger',
      expectedTool: 'search_flights'
    },
    {
      message: 'Plan my entire trip to Barcelona from Dallas, May 10-17, 2025, 2 people',
      expectedTool: 'search_combined_travel'
    },
    {
      message: 'Tell me more details about hotel_001',
      expectedTool: 'get_hotel_details'
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n📝 Testing: "${testCase.message}"`);
      console.log(`Expected tool: ${testCase.expectedTool}`);
      
      const response = await api.post('/chat', {
        message: testCase.message
      });

      if (response.data.toolCalls && response.data.toolCalls.length > 0) {
        const actualTool = response.data.toolCalls[0].name;
        console.log(`Actual tool: ${actualTool}`);
        
        if (actualTool === testCase.expectedTool) {
          console.log('✅ Correct tool selected!');
        } else {
          console.log('❌ Wrong tool selected');
        }
      } else {
        console.log('❌ No tools called');
      }
      
    } catch (error) {
      console.error('Error:', error.response?.data?.error || error.message);
    }
  }
}

/**
 * Example: Verify MCP integration is working
 */
async function verifyMCPIntegrationExample() {
  console.log('\n=== Verify MCP Integration Example ===');
  console.log('Checking if MCP tools are properly integrated with LangChain');
  
  try {
    // Test a simple hotel search
    const response = await api.post('/chat', {
      message: 'Search for hotels in San Francisco for 1 guest from June 1-3, 2025'
    });

    console.log('User:', 'Search for hotels in San Francisco for 1 guest from June 1-3, 2025');
    
    // Check if tools were called
    if (response.data.toolCalls) {
      console.log('\n✅ LangChain tool calling is working!');
      console.log('Tools called:', response.data.toolCalls.map(t => t.name).join(', '));
      
      // Check if MCP was actually called
      if (response.data.toolResults) {
        console.log('\n✅ MCP integration is working!');
        response.data.toolResults.forEach(result => {
          console.log(`- ${result.toolName}: ${result.success ? 'Success' : 'Failed'}`);
          if (result.success) {
            try {
              const parsedResult = JSON.parse(result.result);
              if (parsedResult.hotels) {
                console.log(`  Found ${parsedResult.hotels.length} hotels`);
              }
            } catch (e) {
              console.log('  Result parsing failed');
            }
          }
        });
      } else {
        console.log('\n❌ MCP integration not working - no tool results');
      }
    } else {
      console.log('\n❌ LangChain tool calling not working - no tools called');
    }

    console.log('\n🤖 Claude Response:', response.data.response);

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

/**
 * Run all true LangChain + MCP examples
 */
async function runTrueLangChainMCPExamples() {
  console.log('🔧 True LangChain + MCP Integration Test');
  console.log('==========================================');
  console.log('This tests whether Claude automatically calls MCP tools via LangChain');

  // Check if server is running
  try {
    await api.get('/health');
    console.log('✅ Server is running');
  } catch (error) {
    console.error('❌ Server is not running. Please start the server first with: npm run dev');
    return;
  }

  // Run verification first
  await verifyMCPIntegrationExample();
  
  // Run other examples
  await immediateToolCallingExample();
  await oneMessageTripPlanningExample();
  await progressiveToolCallingExample();
  await toolSelectionIntelligenceExample();

  console.log('\n🎯 Integration Test Complete!');
  console.log('\nExpected behavior:');
  console.log('✅ Claude should automatically call MCP tools when given travel requests');
  console.log('✅ Tools should be called immediately if enough information is provided');
  console.log('✅ Combined travel tool should be used for complete trip planning');
  console.log('✅ Tool results should be processed and included in natural language responses');
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTrueLangChainMCPExamples().catch(console.error);
}

export {
  immediateToolCallingExample,
  oneMessageTripPlanningExample,
  progressiveToolCallingExample,
  toolSelectionIntelligenceExample,
  verifyMCPIntegrationExample,
  runTrueLangChainMCPExamples
};
